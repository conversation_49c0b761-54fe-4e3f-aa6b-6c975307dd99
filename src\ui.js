// UI管理模块
class UIManager {
    constructor() {
        this.currentView = 'main';
        this.isLoading = false;
        this.notifications = [];
        this.originalResultText = ''; // 保存原始识别结果
        this.baseConfigEventsBound = false; // 标记基础配置事件是否已绑定
        this.isCapturing = false; // 标记是否正在截图中
        this.imageZoomManager = null; // 图片缩放管理器
        this.imageTranslateInputZoomManager = null; // 图片翻译输入区域缩放管理器
        this.imageTranslateResultZoomManager = null; // 图片翻译结果区域缩放管理器
        this.languageSelectorsInitialized = false; // 标记语言选择器是否已初始化
        this.backupRestoreEventsInitialized = false; // 标记备份恢复事件是否已初始化

        // 图片翻译相关状态
        this.currentImageTranslateBase64 = null;
        this.currentImageTranslateResult = null;

        // 翻译按钮控制
        this.translateButtonsTimer = null; // 延迟隐藏计时器

        // 保存原始占位符文本（在页面加载时获取）
        this.originalPlaceholders = {
            singleColumn: '',
            dualColumn: '',
            translateResult: ''
        };

        // 用户是否已经手动选择过模型服务商
        this.hasUserSelectedModelService = false;

        // 多模型翻译相关
        this.selectedTranslateModels = []; // 选中的翻译模型数组
        this.translateResults = {}; // 存储每个模型的翻译结果
        this.currentDisplayModel = null; // 当前显示的模型结果
    }

    // 初始化UI
    init() {
        this.bindGlobalEvents();
        this.setupKeyboardShortcuts();
        this.setupPasswordToggles();
        this.setupResultControls();
        this.setupThemeToggle();
        this.loadTheme();
        this.setupThemeListener();
        this.initPromptConfigModal();
        this.setupTranslateButtonsControl(); // 初始化翻译按钮控制
        this.initErrorHandling();
        // 移除重复的模型状态事件监听，统一在main.js中处理
        // this.bindModelStatusEvents();
        this.initServiceIcons();
        this.bindOCRTestButtonEvents();
        this.bindTranslateConfigEvents(); // 添加翻译配置事件绑定
        this.initCustomTooltips(); // 初始化自定义工具提示

        // 初始化占位符（确保在DOM加载完成后保存原始占位符）
        this.initOriginalPlaceholders();

        // 恢复传统服务商测试按钮状态
        this.restoreTraditionalServiceTestButtonStates();
    }

    // 绑定模型状态变化事件
    bindModelStatusEvents() {
        window.addEventListener('modelStatusChanged', (event) => {
            const { platform, modelId, status } = event.detail;
            this.handleModelStatusChange(platform, modelId, status);
        });
    }

    // 处理模型状态变化
    handleModelStatusChange(platform, modelId, status) {
        // 如果当前在基础配置页面，更新模型选择菜单
        const baseConfigPage = document.getElementById('base-config-page');
        if (baseConfigPage && baseConfigPage.style.display !== 'none') {
            // 延迟更新，确保状态已经保存
            setTimeout(() => {
                const modes = ['text', 'table', 'formula', 'markdown'];
                modes.forEach(mode => {
                    this.initRecognitionModeModelMenu(mode);
                });
            }, 100);
        }

        // 如果当前在模型服务页面，更新对应平台的模型列表显示
        const modelServicePage = document.getElementById('model-service-page');
        if (modelServicePage && modelServicePage.style.display !== 'none') {
            // 检查当前显示的是否是该平台的配置
            const platformConfig = document.getElementById(`${platform}-config`);
            if (platformConfig && platformConfig.style.display !== 'none') {
                // 延迟更新，确保状态已经保存
                setTimeout(() => {
                    if (window.ocrPlugin && window.ocrPlugin.renderModelList) {
                        window.ocrPlugin.renderModelList(platform);
                    }
                }, 100);
            }
        }
    }

    // 安全访问window.ocrPlugin的辅助函数
    getOcrPlugin() {
        try {
            return window.ocrPlugin || null;
        } catch (error) {
            console.warn('访问window.ocrPlugin时出错:', error);
            return null;
        }
    }

    // 初始化错误处理
    initErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('🔴 未处理的Promise错误:', event.reason);
            console.error('🔴 错误堆栈:', event.reason?.stack);
            // 检查是否是window.ocrPlugin相关的错误
            if (event.reason && event.reason.message && event.reason.message.includes('ocrPlugin')) {
                console.warn('检测到ocrPlugin相关错误，可能是初始化时序问题');
            }
            // 显示用户友好的错误提示
            this.showError('发生了一个异步错误，请查看控制台了解详情');
            // 不阻止默认处理，让开发者能看到错误
        });

        // 捕获未处理的JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('🔴 JavaScript错误:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
            // 检查是否是window.ocrPlugin相关的错误
            if (event.error && event.error.message && event.error.message.includes('ocrPlugin')) {
                console.warn('检测到ocrPlugin相关错误，可能是初始化时序问题');
            }
            // 显示用户友好的错误提示
            this.showError('发生了一个JavaScript错误，请查看控制台了解详情');
            // 不阻止默认处理，让开发者能看到错误
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                console.error('🔴 资源加载错误:', {
                    type: event.target.tagName,
                    src: event.target.src || event.target.href,
                    message: event.message
                });
            }
        }, true);
    }

    // 绑定全局事件
    bindGlobalEvents() {
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.adjustLayout();
        });

        // 拖拽文件支持
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.showDropZone();
        });

        document.addEventListener('dragleave', (e) => {
            if (!e.relatedTarget) {
                this.hideDropZone();
            }
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            this.hideDropZone();
            this.handleFileDrop(e);
        });

        // 注释掉全局window.blur事件监听器，避免点击窗口边框等正常UI交互时误触发清空
        // 现有的visibilitychange事件和utools.onPluginOut()已经能够处理真正需要清空的场景
        // window.addEventListener('blur', () => {
        //     // 当窗口失去焦点时，清空翻译内容
        //     if (this.currentView === 'translate') {
        //         this.clearTranslateContentSilently();
        //     }
        // });

        // 监听页面可见性变化（用户切换标签页或最小化窗口）
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 检查配置选项决定是否自动清空
                const config = window.ocrPlugin?.configManager?.getConfig();

                if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
                    this.clearTranslateContentSilently();
                } else if (this.currentView === 'main' && config?.ui?.autoCleanOCR) {
                    this.clearOCRContentSilently();
                } else if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
                    this.clearImageTranslateContentSilently();
                }
            }
        });
    }

    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        // 移除之前的事件监听器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 创建新的事件处理器
        this.keydownHandler = (e) => {
            // 如果正在录入快捷键，不处理全局快捷键
            if (this.recordingInput) {
                return;
            }

            // 获取快捷键配置
            const shortcuts = window.ocrPlugin?.configManager?.getShortcuts() || {};
            const pressedShortcut = this.getShortcutFromEvent(e);

            // 检查是否匹配配置的快捷键
            for (const [action, shortcut] of Object.entries(shortcuts)) {
                if (pressedShortcut === shortcut) {
                    // 先尝试执行快捷键操作，如果真正处理了才阻止默认行为
                    const handled = this.executeShortcutAction(action);
                    if (handled) {
                        e.preventDefault();
                    }
                    return;
                }
            }

            // 保留一些固定的快捷键作为后备
            // Ctrl/Cmd + S: 保存配置
            if ((e.ctrlKey || e.metaKey) && e.key === 's' && this.currentView === 'config') {
                e.preventDefault();
                const saveBtn = document.getElementById('save-config-btn');
                if (saveBtn) {
                    saveBtn.click();
                }
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
    }

    // 从键盘事件获取快捷键字符串
    getShortcutFromEvent(e) {
        const parts = [];
        if (e.ctrlKey || e.metaKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.shiftKey) parts.push('Shift');

        let key = e.key;
        if (key === ' ') key = 'Space';
        else if (key === 'Escape') key = 'Escape';
        else if (key === 'Enter') key = 'Enter';
        else if (key === 'Tab') key = 'Tab';
        else if (key === 'Backspace') key = 'Backspace';
        else if (key === 'Delete') key = 'Delete';
        else if (key.startsWith('Arrow')) key = key.replace('Arrow', '');
        else if (key.startsWith('F') && /^F\d+$/.test(key)) key = key;
        else if (key.length === 1) key = key.toUpperCase();

        parts.push(key);
        return parts.join('+');
    }

    // 执行快捷键对应的操作
    executeShortcutAction(action) {
        switch (action) {
            case 'copyResult':
                // 仅在OCR页面、翻译页面生效，其他页面不处理（保持系统默认行为）
                if (this.currentView === 'main') {
                    // OCR页面：触发复制按钮
                    const copyBtn = document.getElementById('copy-btn');
                    if (copyBtn) {
                        copyBtn.click();
                        return true; // 表示已处理
                    }
                } else if (this.currentView === 'translate') {
                    // 翻译页面：触发翻译复制按钮
                    const translateCopyBtn = document.getElementById('translate-copy-btn');
                    if (translateCopyBtn) {
                        translateCopyBtn.click();
                        return true; // 表示已处理
                    }
                }
                // 其他页面不处理，保持系统默认复制行为
                return false; // 表示未处理，允许系统默认行为

            case 'takeScreenshot':
                // 在所有页面生效
                const screenshotBtn = document.getElementById('screenshot-btn');
                if (screenshotBtn) {
                    screenshotBtn.click();
                    return true;
                } else {
                    // 如果不在OCR页面，先跳转到OCR页面再截图
                    if (this.currentView !== 'main') {
                        this.showMainView();
                        setTimeout(() => {
                            const btn = document.getElementById('screenshot-btn');
                            if (btn) {
                                btn.click();
                            }
                        }, 100);
                        return true;
                    }
                }
                return false;

            case 'reRecognize':
                if (this.currentView === 'main') {
                    const reRecognizeBtn = document.getElementById('re-recognize-btn');
                    if (reRecognizeBtn && !reRecognizeBtn.disabled) {
                        reRecognizeBtn.click();
                        return true;
                    }
                }
                return false;

            case 'clearResult':
                // 仅在OCR页面、翻译页面生效
                if (this.currentView === 'main') {
                    this.clearResults();
                    return true;
                } else if (this.currentView === 'translate') {
                    // 翻译页面：清空翻译内容
                    this.clearTranslateContent();
                    return true;
                }
                return false;

            case 'openSettings':
                if (this.currentView === 'main') {
                    this.showConfigView();
                    return true;
                }
                return false;

            case 'backToMain':
                if ((this.currentView === 'config' || this.currentView === 'translate') && !this.isCapturing) {
                    this.showMainView();
                    return true;
                } else if (this.currentView === 'main') {
                    // 在隐藏窗口前根据配置决定是否清空翻译内容
                    const config = window.ocrPlugin?.configManager?.getConfig();
                    if (config?.ui?.autoCleanTranslate) {
                        this.clearTranslateContentSilently();
                    }
                    window.ocrAPI?.hideMainWindow?.();
                    return true;
                }
                return false;

            case 'switchToText':
                if (this.currentView === 'main') {
                    this.switchRecognitionMode('text');
                    return true;
                }
                return false;

            case 'switchToTable':
                if (this.currentView === 'main') {
                    this.switchRecognitionMode('table');
                    return true;
                }
                return false;

            case 'switchToFormula':
                if (this.currentView === 'main') {
                    this.switchRecognitionMode('formula');
                    return true;
                }
                return false;

            case 'switchToMarkdown':
                if (this.currentView === 'main') {
                    this.switchRecognitionMode('markdown');
                    return true;
                }
                return false;

            // 新增快捷键操作
            case 'triggerTranslation':
                this.handleTriggerTranslation();
                return true;

            case 'toggleTheme':
                this.toggleTheme();
                return true;

            case 'toggleLineBreakMode':
                const lineBreakHandled = this.handleToggleLineBreakMode();
                return lineBreakHandled;

            case 'switchTranslationModel':
                const modelHandled = this.handleSwitchTranslationModel();
                return modelHandled;

            case 'openSettingsPage':
                this.showBaseConfigPage();
                return true;

            case 'openHistoryPage':
                this.showHistoryView();
                return true;

            case 'switchHistoryCategory':
                const categoryHandled = this.handleSwitchHistoryCategory();
                return categoryHandled;

            case 'openOCRPage':
                this.showMainView();
                return true;

            case 'openTranslationPage':
                this.showTranslateView();
                return true;

            case 'openModelServicePage':
                this.showModelServicePage();
                return true;

            default:
                return false; // 未知快捷键，不处理
        }
    }

    // 清空识别结果
    clearResults() {
        const resultText = document.getElementById('result-text');
        const rawResultText = document.getElementById('raw-result-text');
        const renderedResult = document.getElementById('rendered-result');

        if (resultText) {
            resultText.value = '';
            resultText.placeholder = this.originalPlaceholders?.singleColumn || '识别结果将在这里显示...';
        }

        if (rawResultText) {
            rawResultText.value = '';
            rawResultText.placeholder = this.originalPlaceholders?.dualColumn || '原始结果...';
        }

        if (renderedResult) {
            renderedResult.innerHTML = '';
        }

        this.showNotification('已清空识别结果', 'success');
    }

    // 获取识别模式对应的图标SVG（与main.js保持一致）
    getModeIconSVG(mode) {
        const iconSVGs = {
            'text': `<path d="M15 12h6"/><path d="M15 6h6"/><path d="m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13"/><path d="M3 18h18"/><path d="M3.92 11h6.16"/>`,
            'table': `<path d="M15 3v18"/><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M21 9H3"/><path d="M21 15H3"/>`,
            'formula': `<path d="M3 20h4.5a.5.5 0 0 0 .5-.5v-.282a.52.52 0 0 0-.247-.437 8 8 0 1 1 8.494-.001.52.52 0 0 0-.247.438v.282a.5.5 0 0 0 .5.5H21"/>`,
            'markdown': `<rect width="18" height="18" x="3" y="3" rx="2"/><g transform="rotate(90 12 12)"><path d="M16 8.9V7H8l4 5-4 5h8v-1.9"/></g>`
        };
        return iconSVGs[mode] || iconSVGs['text'];
    }

    // 切换识别模式
    switchRecognitionMode(mode) {
        // 调用主插件的识别模式选择方法
        if (window.ocrPlugin && window.ocrPlugin.selectRecognitionMode) {
            window.ocrPlugin.selectRecognitionMode(mode);
        } else {
            // 备用方案：直接更新UI
            const modeNames = {
                'text': '文字识别',
                'table': '表格识别',
                'formula': '公式识别',
                'markdown': 'MD识别'
            };

            const modeName = modeNames[mode] || '文字';
            const modeText = document.querySelector('#recognition-mode-btn .mode-text');
            if (modeText) {
                modeText.textContent = modeName;
            }

            // 更新主按钮图标
            const modeIcon = document.querySelector('#mode-icon');
            if (modeIcon) {
                modeIcon.innerHTML = this.getModeIconSVG(mode);
            }

            // 更新菜单中的选中状态
            const options = document.querySelectorAll('.mode-option');
            options.forEach(option => {
                option.classList.remove('active');
                if (option.getAttribute('data-mode') === mode) {
                    option.classList.add('active');
                }
            });

            this.showNotification(`已切换到${modeNames[mode] || '文字识别'}模式`, 'success');
        }
    }

    // 新增快捷键处理方法

    // 处理触发翻译功能
    handleTriggerTranslation() {
        switch (this.currentView) {
            case 'main':
                // OCR页面：触发导航栏的翻译按钮
                const ocrTranslateBtn = document.getElementById('translate-btn');
                if (ocrTranslateBtn) {
                    ocrTranslateBtn.click();
                }
                break;
            case 'translate':
                // 翻译页面：触发翻译按钮
                const translateBtn = document.getElementById('translate-execute-btn');
                if (translateBtn && !translateBtn.disabled) {
                    translateBtn.click();
                }
                break;
            case 'config':
            case 'history':
                // 设置页面：返回翻译页面
                this.showTranslateView();
                break;
            default:
                // 其他页面静默忽略
                break;
        }
    }

    // 处理换行符模式切换
    handleToggleLineBreakMode() {
        // 仅在OCR页面生效
        if (this.currentView !== 'main') {
            return false;
        }

        const linebreakToggleBtn = document.getElementById('linebreak-toggle-btn');
        if (linebreakToggleBtn) {
            // 触发点击事件
            linebreakToggleBtn.click();
            return true;
        }
        return false;
    }

    // 处理翻译模型切换
    handleSwitchTranslationModel() {
        if (this.currentView !== 'translate') {
            return false; // 只在翻译页面有效
        }

        // 获取翻译页面导航栏左侧的前4个默认翻译模型按钮
        const modelButtons = document.querySelectorAll('#translate-model-selector .translate-model-btn');
        if (modelButtons.length === 0) {
            return false;
        }

        // 找到当前激活的按钮
        let currentIndex = -1;
        modelButtons.forEach((btn, index) => {
            if (btn.classList.contains('active')) {
                currentIndex = index;
            }
        });

        // 切换到下一个模型（循环）
        const nextIndex = (currentIndex + 1) % modelButtons.length;
        const nextButton = modelButtons[nextIndex];
        if (nextButton) {
            nextButton.click();
            return true;
        }
        return false;
    }

    // 处理历史记录分类切换
    handleSwitchHistoryCategory() {
        if (this.currentView !== 'history') {
            return false; // 只在历史记录页面有效
        }

        // 获取历史记录页面左侧的OCR和翻译切换按钮
        const ocrBtn = document.getElementById('history-ocr-btn');
        const translateBtn = document.getElementById('history-translate-type-btn');

        if (!ocrBtn || !translateBtn) {
            return false;
        }

        // 检查当前激活的按钮并切换到另一个
        if (ocrBtn.classList.contains('active')) {
            translateBtn.click();
            return true;
        } else {
            ocrBtn.click();
            return true;
        }
    }

    // 检测文本框中是否有选中的文本
    getSelectedText(textElement) {
        if (!textElement) return '';

        const start = textElement.selectionStart;
        const end = textElement.selectionEnd;

        // 如果有选中文本（选择范围大于0）
        if (start !== end) {
            return textElement.value.substring(start, end);
        }

        return '';
    }

    // 获取页面中选中的文本（支持div和textarea）
    getPageSelectedText() {
        const selection = window.getSelection();
        if (selection && selection.toString().trim()) {
            return selection.toString().trim();
        }
        return '';
    }

    // 智能复制：优先复制选中文本，无选中时复制完整内容
    getSmartCopyText() {
        // 首先检查页面中是否有任何选中的文本（包括div中的文本）
        const pageSelectedText = this.getPageSelectedText();
        if (pageSelectedText) {
            return pageSelectedText;
        }

        // 检查当前是单栏还是双栏模式
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');

        const isSingleMode = singleContainer && singleContainer.style.display !== 'none';
        const isDualMode = dualContainer && dualContainer.style.display !== 'none';

        let targetElement = null;
        let fallbackText = '';

        if (isSingleMode) {
            // 单栏模式：检查主文本框
            targetElement = document.getElementById('result-text');
            if (targetElement) {
                // 检查textarea中的选中文本
                const selectedText = this.getSelectedText(targetElement);
                if (selectedText) {
                    return selectedText;
                }
                fallbackText = targetElement.value;
            }
        } else if (isDualMode) {
            // 双栏模式：检查原始结果文本框
            const rawResultText = document.getElementById('raw-result-text');
            if (rawResultText) {
                // 检查textarea中的选中文本
                const selectedText = this.getSelectedText(rawResultText);
                if (selectedText) {
                    return selectedText;
                }
                fallbackText = rawResultText.value;
            }
        }

        // 如果没有选中文本，返回完整内容
        return fallbackText;
    }

    // 显示主视图
    showMainView() {
        // 根据当前页面和配置决定是否清空内容
        const config = this.getOcrPlugin()?.configManager?.getConfig();
        if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
            this.clearTranslateContentSilently();
        } else if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
            this.clearImageTranslateContentSilently();
        }

        // 如果当前在模型服务页面，保存选中的服务商
        this.saveCurrentSelectedModelService();

        // 清理图片翻译按钮控制事件
        this.cleanupImageTranslateButtonsControl();

        document.getElementById('config-view').style.display = 'none';
        document.getElementById('history-view').style.display = 'none';
        document.getElementById('translate-view').style.display = 'none';
        document.getElementById('image-translate-view').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentView = 'main';
        this.adjustLayout();

        // 清除配置导航栏的高亮状态
        this.clearConfigNavButtons();

        // 从配置页面返回主界面时，同步最新的服务状态
        const ocrPlugin = this.getOcrPlugin();
        if (ocrPlugin) {
            try {
                // 重新加载配置以确保同步
                ocrPlugin.config = ocrPlugin.configManager.getConfig();

                // 更新主界面模型按钮显示（基于当前识别模式配置）
                ocrPlugin.updateMainInterfaceModelFromConfig();

                // 清除服务切换菜单缓存，确保下次打开时使用最新数据
                const serviceMenu = document.getElementById('service-switch-menu');
                if (serviceMenu) {
                    serviceMenu.innerHTML = '';
                }

                // 使用强制同步方法确保按钮状态与实际配置完全一致
                if (ocrPlugin.forceSyncMainInterfaceButtonState) {
                    ocrPlugin.forceSyncMainInterfaceButtonState();
                } else {
                    // 降级到原有逻辑
                    ocrPlugin.updateMainPageStatus();
                }
            } catch (error) {
                console.error('更新主界面状态时出错:', error);
                // 如果出错，至少要确保状态显示
                ocrPlugin.updateMainPageStatus();
            }
        }
    }

    // 显示配置视图
    showConfigView() {
        // 根据当前页面和配置决定是否清空内容
        const config = this.getOcrPlugin()?.configManager?.getConfig();
        if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
            this.clearTranslateContentSilently();
        } else if (this.currentView === 'main' && config?.ui?.autoCleanOCR) {
            this.clearOCRContentSilently();
        } else if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
            this.clearImageTranslateContentSilently();
        } else if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
            this.clearImageTranslateContentSilently();
        }

        // 清理图片翻译按钮控制事件
        this.cleanupImageTranslateButtonsControl();

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'block';
        document.getElementById('history-view').style.display = 'none';
        document.getElementById('translate-view').style.display = 'none';
        this.currentView = 'config';
        this.adjustLayout();

        // 检查是否有记忆的页面，如果没有则默认显示基础配置页面
        const lastConfigPage = this.getStorageItem('lastConfigPage');
        if (lastConfigPage === 'model-service') {
            this.showModelServicePage();
        } else if (lastConfigPage === 'history') {
            this.showHistoryView();
        } else {
            // 默认显示基础配置页面
            this.showBaseConfigPage();
        }

        // 注意：不在这里执行同步逻辑，因为各个页面显示方法会处理
        // 避免重复调用 switchConfigSection 和状态检测
    }

    // 显示基础配置页面
    showBaseConfigPage() {
        // 根据当前页面和配置决定是否清空内容
        const config = window.ocrPlugin?.configManager?.getConfig();
        if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
            this.clearTranslateContentSilently();
        } else if (this.currentView === 'main' && config?.ui?.autoCleanOCR) {
            this.clearOCRContentSilently();
        }

        // 如果当前在模型服务页面，保存选中的服务商
        this.saveCurrentSelectedModelService();

        // 首先确保显示配置视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'block';
        document.getElementById('history-view').style.display = 'none';
        document.getElementById('translate-view').style.display = 'none';
        this.currentView = 'config';
        this.adjustLayout();

        // 隐藏所有配置页面
        document.querySelectorAll('.config-page').forEach(page => {
            page.style.display = 'none';
        });

        // 显示基础配置页面
        document.getElementById('base-config-page').style.display = 'flex';

        // 更新导航栏按钮状态
        this.updateConfigNavButtons('base-config');

        // 初始化基础配置页面
        this.initBaseConfigPage();

        // 加载识别模式配置以确保UI同步
        this.loadRecognitionModeConfigs();

        // 记忆当前页面
        this.setStorageItem('lastConfigPage', 'base-config');

        // 恢复传统服务商测试按钮状态（同步执行，避免视觉闪烁）
        this.restoreTraditionalServiceTestButtonStates();
    }

    // 显示模型服务页面
    showModelServicePage() {
        // 根据当前页面和配置决定是否清空内容
        const config = window.ocrPlugin?.configManager?.getConfig();
        if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
            this.clearTranslateContentSilently();
        } else if (this.currentView === 'main' && config?.ui?.autoCleanOCR) {
            this.clearOCRContentSilently();
        }

        // 首先确保显示配置视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'block';
        document.getElementById('history-view').style.display = 'none';
        this.currentView = 'config';
        this.adjustLayout();

        // 隐藏所有配置页面
        document.querySelectorAll('.config-page').forEach(page => {
            page.style.display = 'none';
        });

        // 显示模型服务页面
        document.getElementById('model-service-page').style.display = 'flex';

        // 更新导航栏按钮状态
        this.updateConfigNavButtons('model-service');

        // 检查所有AI平台是否需要首次自动获取模型列表
        this.checkAllPlatformsFirstTimeAutoFetch();

        // 强制刷新服务商列表（确保新添加的服务商能立即显示）
        this.forceRefreshServiceList();

        // 绑定服务列表点击事件
        this.bindServiceListEvents();

        // 绑定网络代理按钮事件
        this.bindProxyButtonEvents();

        // 初始化代理按钮状态
        this.initProxyButtonStates();

        // 同步配置界面的服务选择（在渲染完成后执行）
        if (window.ocrPlugin) {
            // 获取上次在模型服务页面选中的服务商，如果没有则使用当前配置的服务
            const lastSelectedService = this.getStorageItem('lastSelectedModelService');

            // 如果有存储的选择，说明用户之前已经手动选择过
            if (lastSelectedService) {
                this.hasUserSelectedModelService = true;
            }

            const currentService = lastSelectedService || window.ocrPlugin.config.service || 'baidu';

            // 如果使用了存储的服务商选择，同步更新全局配置
            if (lastSelectedService && lastSelectedService !== window.ocrPlugin.config.service) {
                window.ocrPlugin.config.service = lastSelectedService;
                // 保存配置以确保持久化
                window.ocrPlugin.configManager.saveConfig(window.ocrPlugin.config);
            }

            // 更新配置界面的服务选择下拉框（保持兼容性）
            const serviceSelect = document.getElementById('ocr-service');
            if (serviceSelect && serviceSelect.value !== currentService) {
                serviceSelect.value = currentService;
            }

            // 更新服务列表的选中状态（在DOM渲染完成后）
            this.updateServiceListSelection(currentService);

            // 切换到对应的配置区域
            this.switchConfigSection(currentService);

            // 如果是AI服务，还需要同步平台配置
            if (['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(currentService)) {
                // 确保AI平台配置正确加载
                window.ocrPlugin.loadAIPlatformConfigs();
            }
        }

        // 更新所有服务的状态指示器
        this.updateAllServiceStatusIndicators();

        // 加载翻译地域配置
        this.loadTranslateRegionConfigs();

        // 记忆当前页面
        this.setStorageItem('lastConfigPage', 'model-service');

        // 恢复传统服务商测试按钮状态（同步执行，避免视觉闪烁）
        this.restoreTraditionalServiceTestButtonStates();
    }

    // 检查所有AI平台是否需要首次自动获取模型列表
    async checkAllPlatformsFirstTimeAutoFetch() {
        const platforms = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'];

        for (const platform of platforms) {
            try {
                await this.checkPlatformFirstTimeAutoFetch(platform);
            } catch (error) {
                console.error(`检查${platform}首次自动获取失败:`, error);
            }
        }
    }

    // 检查平台是否需要首次自动获取模型列表
    async checkPlatformFirstTimeAutoFetch(platform) {
        try {
            // 检查是否已经有存储的模型列表
            const storedModels = window.ocrPlugin.getStoredPlatformModels(platform);

            if (!storedModels || storedModels.length === 0) {
                // 首次进入，检查是否有API Key配置
                const apiKey = window.ocrPlugin.getElementValue(`${platform}-api-key`);
                if (apiKey || platform === 'utools') {
                    // 自动获取模型列表
                    await this.autoFetchPlatformModels(platform);
                }
            } else {
                // 已有存储的模型列表，直接渲染
                window.ocrPlugin.renderModelList(platform);
            }
        } catch (error) {
            console.error(`检查${platform}首次自动获取失败:`, error);
        }
    }

    // 检查Google平台是否需要首次自动获取模型列表（保持向后兼容）
    async checkGoogleFirstTimeAutoFetch() {
        return this.checkPlatformFirstTimeAutoFetch('google');
    }

    // 统一的存储读取方法
    getStorageItem(key) {
        try {
            // 优先使用uTools的dbStorage，如果不可用则回退到localStorage
            if (typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.getItem) {
                return utools.dbStorage.getItem(key);
            } else {
                return localStorage.getItem(key);
            }
        } catch (error) {
            console.warn(`读取存储项 ${key} 失败:`, error);
            return null;
        }
    }

    // 统一的存储写入方法
    setStorageItem(key, value) {
        try {
            // 优先使用uTools的dbStorage，如果不可用则回退到localStorage
            if (typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem) {
                utools.dbStorage.setItem(key, value);
                return true;
            } else {
                localStorage.setItem(key, value);
                return true;
            }
        } catch (error) {
            console.warn(`写入存储项 ${key} 失败:`, error);
            return false;
        }
    }

    // 统一的存储删除方法
    removeStorageItem(key) {
        try {
            // 优先使用uTools的dbStorage，如果不可用则回退到localStorage
            if (typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.removeItem) {
                utools.dbStorage.removeItem(key);
                return true;
            } else {
                localStorage.removeItem(key);
                return true;
            }
        } catch (error) {
            console.error('删除存储数据失败:', error);
            return false;
        }
    }



    // 获取存储的Google模型列表
    getStoredGoogleModels() {
        try {
            const stored = this.getStorageItem('google_fetched_models');
            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                return typeof stored === 'string' ? JSON.parse(stored) : stored;
            }
            return null;
        } catch (error) {
            console.error('获取存储的Google模型列表失败:', error);
            return null;
        }
    }

    // 存储Google模型列表
    storeGoogleModels(models) {
        try {
            // uTools的dbStorage可以直接存储对象，localStorage需要JSON序列化
            const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
            const modelsToStore = isUToolsStorage ? models : JSON.stringify(models);
            const timeToStore = isUToolsStorage ? Date.now() : Date.now().toString();

            this.setStorageItem('google_fetched_models', modelsToStore);
            this.setStorageItem('google_models_fetch_time', timeToStore);
        } catch (error) {
            console.error('存储Google模型列表失败:', error);
        }
    }

    // 自动获取平台模型列表
    async autoFetchPlatformModels(platform) {
        try {
            const apiKey = window.ocrPlugin.getElementValue(`${platform}-api-key`);
            const baseUrl = window.ocrPlugin.getElementValue(`${platform}-base-url`);

            if (!apiKey && platform !== 'utools') {
                return;
            }

            // 获取模型列表
            const models = await window.ocrPlugin.modelManager.getModels(platform, apiKey, baseUrl);

            if (models && models.length > 0) {
                // 存储模型列表
                window.ocrPlugin.storePlatformModels(platform, models);

                // 渲染模型列表
                window.ocrPlugin.renderModelList(platform);
            }
        } catch (error) {
            console.error(`自动获取${platform}模型列表失败:`, error);
        }
    }

    // 自动获取Google模型列表（保持向后兼容）
    async autoFetchGoogleModels() {
        return this.autoFetchPlatformModels('google');
    }

    // 更新配置页面导航栏按钮状态
    updateConfigNavButtons(activePage) {
        // 移除所有导航按钮的active状态（支持新旧两种按钮类名）
        document.querySelectorAll('.config-bottom-nav .nav-btn, .config-bottom-nav .control-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 为当前页面的导航按钮添加active状态
        // 修复按钮选择逻辑，确保准确匹配
        let targetButtons = [];
        if (activePage === 'base-config') {
            targetButtons = document.querySelectorAll('[id$="base-config-btn"]');
        } else if (activePage === 'model-service') {
            targetButtons = document.querySelectorAll('[id$="model-service-btn"]');
        } else if (activePage === 'history') {
            targetButtons = document.querySelectorAll('[id$="history-btn"]');
        }

        targetButtons.forEach(btn => {
            btn.classList.add('active');
        });
    }

    // 清除配置导航栏按钮的高亮状态
    clearConfigNavButtons() {
        // 移除所有导航按钮的active状态
        document.querySelectorAll('.config-bottom-nav .nav-btn, .config-bottom-nav .control-btn').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    // 初始化基础配置页面
    initBaseConfigPage() {
        // 绑定配置项点击事件
        this.bindBaseConfigEvents();

        // 初始化默认模型配置
        this.initDefaultModelConfig();

        // 加载配置数据
        this.loadBaseConfigData();

        // 恢复上次选择的配置项，如果没有则默认显示个人中心配置
        const lastBaseConfigSection = this.getStorageItem('lastBaseConfigSection') || 'personal-center';
        this.selectBaseConfigItem(lastBaseConfigSection);
        this.showBaseConfigSection(lastBaseConfigSection);

        // 添加全局点击事件监听器来关闭模型选择菜单（已移除功能）
    }

    // 绑定基础配置页面事件
    bindBaseConfigEvents() {
        // 如果已经绑定过事件，则不重复绑定
        if (this.baseConfigEventsBound) {
            return;
        }

        // 绑定配置项点击事件
        document.querySelectorAll('.config-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const configType = e.currentTarget.dataset.config;
                this.selectBaseConfigItem(configType);
                this.showBaseConfigSection(configType);
            });
        });

        // 绑定提示词配置按钮事件
        const promptConfigButtons = [
            { id: 'text-prompt-config', mode: 'text' },
            { id: 'table-prompt-config', mode: 'table' },
            { id: 'formula-prompt-config', mode: 'formula' },
            { id: 'markdown-prompt-config', mode: 'markdown' }
        ];

        promptConfigButtons.forEach(({ id, mode }) => {
            const button = document.getElementById(id);
            if (button) {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // 检查当前模式是否使用LLM服务
                    const modeConfig = window.ocrPlugin.configManager.getRecognitionModeConfig(mode);
                    const isLLMService = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(modeConfig?.service);

                    if (isLLMService) {
                        this.showPromptConfigModal(mode);
                    } else {
                        this.showNotification('提示词配置仅对AI模型生效', 'warning');
                    }
                });
            }
        });



        // 绑定开关事件
        const autoHideSwitch = document.getElementById('auto-hide-switch');
        const autoReOcrSwitch = document.getElementById('auto-reocr-switch');
        const enableHistorySwitch = document.getElementById('enable-history-switch');
        const autoCopyOcrSwitch = document.getElementById('auto-copy-ocr-switch');
        const autoCopyTranslateSwitch = document.getElementById('auto-copy-translate-switch');
        const autoCloseOcrSwitch = document.getElementById('auto-close-ocr-switch');
        const autoCloseTranslateSwitch = document.getElementById('auto-close-translate-switch');
        const autoTranslateSwitch = document.getElementById('auto-translate-switch');
        const autoModelSwitchSwitch = document.getElementById('auto-model-switch');
        const autoCleanOcrSwitch = document.getElementById('auto-clean-ocr-switch');
        const autoCleanTranslateSwitch = document.getElementById('auto-clean-translate-switch');
        const autoTranslateImageTranslateSwitch = document.getElementById('auto-translate-image-translate-switch');
        const autoCleanImageTranslateSwitch = document.getElementById('auto-clean-image-translate-switch');

        if (autoHideSwitch) {
            autoHideSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoHide', e.target.checked);
            });
        }

        if (autoReOcrSwitch) {
            autoReOcrSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoReOcr', e.target.checked);
            });
        }

        if (enableHistorySwitch) {
            enableHistorySwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('enableHistory', e.target.checked);
                this.toggleHistoryMaxCountRow(e.target.checked);
            });
        }

        // 历史记录最大数量配置
        const historyMaxCountInput = document.getElementById('history-max-count');
        if (historyMaxCountInput) {
            historyMaxCountInput.addEventListener('change', (e) => {
                const value = parseInt(e.target.value);
                if (value >= 10 && value <= 1000) {
                    this.saveBaseConfigSetting('historyMaxCount', value);
                } else {
                    // 如果值无效，恢复到默认值
                    e.target.value = 100;
                    this.saveBaseConfigSetting('historyMaxCount', 100);
                }
            });
        }

        if (autoCopyOcrSwitch) {
            autoCopyOcrSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('copyAfterOCR', e.target.checked);
            });
        }

        if (autoCopyTranslateSwitch) {
            autoCopyTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCopyAfterTranslate', e.target.checked);
            });
        }

        if (autoCloseOcrSwitch) {
            autoCloseOcrSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCloseOCR', e.target.checked);
            });
        }

        if (autoCloseTranslateSwitch) {
            autoCloseTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCloseTranslate', e.target.checked);
            });
        }

        if (autoTranslateSwitch) {
            autoTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoTranslate', e.target.checked);
            });
        }

        if (autoModelSwitchSwitch) {
            autoModelSwitchSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoModelSwitch', e.target.checked);
            });
        }

        if (autoCleanOcrSwitch) {
            autoCleanOcrSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCleanOCR', e.target.checked);
            });
        }

        if (autoCleanTranslateSwitch) {
            autoCleanTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCleanTranslate', e.target.checked);
            });
        }

        if (autoTranslateImageTranslateSwitch) {
            autoTranslateImageTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoTranslateImageTranslate', e.target.checked);
            });
        }

        if (autoCleanImageTranslateSwitch) {
            autoCleanImageTranslateSwitch.addEventListener('change', (e) => {
                this.saveBaseConfigSetting('autoCleanImageTranslate', e.target.checked);
            });
        }

        // 绑定翻译模型添加按钮事件
        const translateAddModelBtn = document.getElementById('translate-add-model-btn');
        if (translateAddModelBtn) {
            translateAddModelBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showTranslateModelSelectModal();
            });
        }

        // 绑定显示使用统计开关事件
        const showUsageStatsSwitch = document.getElementById('show-usage-stats');
        if (showUsageStatsSwitch) {
            showUsageStatsSwitch.addEventListener('change', (e) => {
                this.handlePersonalSettingChange('showUsageStats', e.target.checked);
                this.toggleUsageStatsSection(e.target.checked);
            });
        }

        // 标记事件已绑定
        this.baseConfigEventsBound = true;
    }

    // 切换历史记录最大数量配置行的显示/隐藏
    toggleHistoryMaxCountRow(enabled) {
        const historyMaxCountRow = document.getElementById('history-max-count-row');
        if (historyMaxCountRow) {
            historyMaxCountRow.style.display = enabled ? 'flex' : 'none';
        }
    }

    // 初始化默认模型配置（已移除功能，保留结构）
    initDefaultModelConfig() {
        // 功能已移除，保留方法以维持兼容性
    }

    // 为特定识别类型初始化模型选择（已移除功能，保留结构）
    initModelSelectForType(type) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 初始化全局模型菜单处理器（已移除功能，保留结构）
    initGlobalModelMenuHandler() {
        // 功能已移除，保留方法以维持兼容性
    }

    // 填充可用模型列表（已移除功能，保留结构）
    populateAvailableModelsForMenu(type, menuElement) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 切换模型选择菜单（已移除功能，保留结构）
    toggleModelSelectMenu(type) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 隐藏模型选择菜单（已移除功能，保留结构）
    hideModelSelectMenu(type) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 选择模型（已移除功能，保留结构）
    selectModel(type, selectedModel) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 更新菜单中的选中状态（已移除功能，保留结构）
    updateModelMenuSelection(type, selectedModel) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 填充可用模型列表（用于传统select元素，保持兼容性）
    populateAvailableModels(selectElement) {
        if (!window.ocrPlugin) return;

        // 清空现有选项
        selectElement.innerHTML = '<option value="">请选择模型</option>';

        // 获取所有可用模型（包括测试状态过滤）
        const availableModels = window.ocrPlugin.getAvailableModelsForConfig();

        if (availableModels.length === 0) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '暂无已配置的服务';
            option.disabled = true;
            selectElement.appendChild(option);
            return;
        }

        // 遍历可用模型并添加到下拉菜单
        availableModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.value;
            // 移除所有特殊符号和颜色变化，保持统一样式
            option.textContent = model.name;

            selectElement.appendChild(option);
        });
    }

    // 获取服务配置
    getServiceConfig(serviceName) {
        if (!window.ocrPlugin) return null;

        const config = window.ocrPlugin.config;

        // 对于传统OCR服务，直接返回对应配置
        if (['baidu', 'tencent', 'aliyun'].includes(serviceName)) {
            return config[serviceName];
        }

        // 对于LLM服务，从新的配置结构中获取
        const llmPlatforms = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'];
        if (llmPlatforms.includes(serviceName)) {
            return config[serviceName];
        }

        return null;
    }



    // 更新提示词配置按钮的可见性（已移除功能，保留结构）
    updatePromptConfigVisibility(type, selectedModel) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 判断是否为LLM服务（已移除功能，保留结构）
    isLLMService(selectedModel) {
        // 功能已移除，保留方法以维持兼容性
        return false;
    }

    // 同步到主界面（已移除功能，保留结构）
    syncToMainInterface(type, selectedModel) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 同步模型到主界面（已移除功能，保留结构）
    syncModelToMainInterface(selectedModel) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 保存默认模型配置（已移除功能，保留结构）
    saveDefaultModelConfig(type, key, value) {
        // 功能已移除，保留方法以维持兼容性
    }

    // 获取默认模型配置（已移除功能，保留结构）
    getDefaultModelConfig(type, key) {
        // 功能已移除，保留方法以维持兼容性
        return null;
    }

    // 初始化提示词配置弹窗
    initPromptConfigModal() {
        const modal = document.getElementById('prompt-config-modal');
        const closeBtn = document.getElementById('prompt-modal-close');
        const saveHeaderBtn = document.getElementById('prompt-save-header-btn');
        const resetHeaderBtn = document.getElementById('prompt-reset-header-btn');

        // 关闭弹窗事件
        const closeModal = () => {
            modal.style.display = 'none';
            this.currentPromptMode = null;
        };

        closeBtn.addEventListener('click', closeModal);

        // 点击背景关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 保存提示词
        saveHeaderBtn.addEventListener('click', () => {
            this.savePromptConfig();
        });

        // 重置提示词
        resetHeaderBtn.addEventListener('click', () => {
            this.resetPromptConfig();
        });
    }

    // 显示提示词配置弹窗
    showPromptConfigModal(mode) {
        const modal = document.getElementById('prompt-config-modal');
        const title = document.getElementById('prompt-modal-title');
        const textarea = document.getElementById('prompt-textarea');

        // 设置当前配置的模式
        this.currentPromptMode = mode;

        // 设置弹窗标题
        const modeNames = {
            'text': '文字识别',
            'table': '表格识别',
            'formula': '公式识别',
            'markdown': 'Markdown识别'
        };
        title.textContent = `配置${modeNames[mode] || ''}提示词`;

        // 加载当前提示词
        const currentPrompt = window.ocrPlugin.configManager.getRecognitionModePrompt(mode);
        textarea.value = currentPrompt;

        // 显示弹窗
        modal.style.display = 'flex';
        textarea.focus();
    }

    // 保存提示词配置
    savePromptConfig() {
        if (!this.currentPromptMode) return;

        const textarea = document.getElementById('prompt-textarea');
        const prompt = textarea.value.trim();

        // 保存提示词
        const result = window.ocrPlugin.configManager.setRecognitionModePrompt(this.currentPromptMode, prompt);

        if (result.success) {
            this.showNotification('提示词配置已保存', 'success');
            // 关闭弹窗
            document.getElementById('prompt-config-modal').style.display = 'none';
            this.currentPromptMode = null;
        } else {
            this.showNotification('保存失败: ' + result.error, 'error');
        }
    }

    // 重置提示词配置
    resetPromptConfig() {
        if (!this.currentPromptMode) return;

        const textarea = document.getElementById('prompt-textarea');

        // 获取默认提示词
        const defaultPrompts = {
            'text': '请准确识别图片中的的所有文字内容，直接输出文字，尽可能保留原文的排版和结构。始终将识别的精确性放在首位。严格遵守"只回复识别结果"的指示，不添加任何解释、提示。',
            'table': '请准确识别图片中的表格内容，以Markdown表格格式输出，保持原有的行列结构。始终将识别的精确性放在首位。严格遵守"只回复识别结果"的指示，不添加任何解释、提示。',
            'formula': '请准确识别图片中的的数学公式，以LaTeX格式输出，如果有多个公式请分行显示。始终将识别的精确性放在首位。严格遵守"只回复识别结果"的指示，不添加任何解释、提示。',
            'markdown': '请准确识别图片中的所有内容，以Markdown格式输出，尽可能保留原文的排版和列表结构，包括标题、段落、列表、表格等结构。始终将识别的精确性放在首位。严格遵守"只回复识别结果"的指示，不添加任何解释、提示。'
        };

        const defaultPrompt = defaultPrompts[this.currentPromptMode] || '';
        textarea.value = defaultPrompt;

        this.showNotification('提示词已重置为默认内容', 'success');
    }

    // 选择基础配置项
    selectBaseConfigItem(configType) {
        // 移除所有配置项的active状态
        document.querySelectorAll('.config-item').forEach(item => {
            item.classList.remove('active');
        });

        // 为当前配置项添加active状态
        const targetItem = document.querySelector(`[data-config="${configType}"]`);
        if (targetItem) {
            targetItem.classList.add('active');
        }

        // 记忆当前选择的配置项
        this.setStorageItem('lastBaseConfigSection', configType);
    }

    // 显示基础配置区域
    showBaseConfigSection(configType) {
        // 隐藏所有配置区域
        document.querySelectorAll('.base-config-section').forEach(section => {
            section.style.display = 'none';
        });

        // 显示目标配置区域
        const targetSection = document.getElementById(`${configType}-config`);
        if (targetSection) {
            targetSection.style.display = 'block';

            // 如果是默认模型配置区域，重新加载识别模式配置
            if (configType === 'default-model') {
                setTimeout(() => {
                    // 重新加载配置以确保同步
                    if (window.ocrPlugin) {
                        window.ocrPlugin.config = window.ocrPlugin.configManager.getConfig();
                    }
                    this.loadRecognitionModeConfigs();
                }, 50);
            }

            // 如果是翻译模型配置区域，加载翻译模型列表
            if (configType === 'translate-model') {
                setTimeout(() => {
                    // 重新加载配置以确保同步
                    if (window.ocrPlugin) {
                        window.ocrPlugin.config = window.ocrPlugin.configManager.getConfig();
                    }
                    this.refreshTranslateModelList();
                }, 50);
            }

            // 如果是快捷键配置区域，初始化快捷键配置
            if (configType === 'shortcuts') {
                this.initShortcutsConfig();
            }

            // 如果是个人中心配置区域，初始化个人中心配置
            if (configType === 'personal-center') {
                this.initPersonalCenterConfig();
            }

            // 如果是备份恢复配置区域，初始化备份恢复功能
            if (configType === 'backup-restore') {
                this.initBackupRestoreConfig();
            }
        }
    }

    // 加载基础配置数据
    loadBaseConfigData() {
        if (window.ocrPlugin) {
            const config = window.ocrPlugin.config;
            const uiConfig = config.ui || {};

            // 加载开关状态
            const autoHideSwitch = document.getElementById('auto-hide-switch');
            const autoReOcrSwitch = document.getElementById('auto-reocr-switch');
            const enableHistorySwitch = document.getElementById('enable-history-switch');
            const autoCopyOcrSwitch = document.getElementById('auto-copy-ocr-switch');
            const autoCopyTranslateSwitch = document.getElementById('auto-copy-translate-switch');
            const autoCloseOcrSwitch = document.getElementById('auto-close-ocr-switch');
            const autoCloseTranslateSwitch = document.getElementById('auto-close-translate-switch');
            const autoTranslateSwitch = document.getElementById('auto-translate-switch');
            const autoModelSwitchSwitch = document.getElementById('auto-model-switch');
            const autoCleanOcrSwitch = document.getElementById('auto-clean-ocr-switch');
            const autoCleanTranslateSwitch = document.getElementById('auto-clean-translate-switch');
            const autoTranslateImageTranslateSwitch = document.getElementById('auto-translate-image-translate-switch');
            const autoCleanImageTranslateSwitch = document.getElementById('auto-clean-image-translate-switch');
            const showUsageStatsSwitch = document.getElementById('show-usage-stats');

            if (autoHideSwitch) {
                autoHideSwitch.checked = uiConfig.autoHide !== false; // 默认为true
            }

            if (autoReOcrSwitch) {
                autoReOcrSwitch.checked = uiConfig.autoReOcr === true; // 默认为false
            }

            // 加载识别模式配置
            this.loadRecognitionModeConfigs();

            if (enableHistorySwitch) {
                enableHistorySwitch.checked = uiConfig.enableHistory !== false; // 默认为true
                // 根据历史记录开关状态显示/隐藏最大数量配置
                this.toggleHistoryMaxCountRow(enableHistorySwitch.checked);
            }

            // 加载历史记录最大数量配置
            const historyMaxCountInput = document.getElementById('history-max-count');
            if (historyMaxCountInput) {
                historyMaxCountInput.value = uiConfig.historyMaxCount || 100; // 默认为100
            }

            // 加载自动复制开关状态
            if (autoCopyOcrSwitch) {
                autoCopyOcrSwitch.checked = uiConfig.copyAfterOCR === true; // 默认为false
            }

            if (autoCopyTranslateSwitch) {
                autoCopyTranslateSwitch.checked = uiConfig.autoCopyAfterTranslate === true; // 默认为false
            }

            // 处理向后兼容性：如果存在旧的autoClose配置，则同时启用两个新开关
            const legacyAutoClose = uiConfig.autoClose === true;

            if (autoCloseOcrSwitch) {
                // 优先使用新配置，如果没有则使用旧配置
                autoCloseOcrSwitch.checked = uiConfig.autoCloseOCR !== undefined ?
                    uiConfig.autoCloseOCR === true : legacyAutoClose;
            }

            if (autoCloseTranslateSwitch) {
                // 优先使用新配置，如果没有则使用旧配置
                autoCloseTranslateSwitch.checked = uiConfig.autoCloseTranslate !== undefined ?
                    uiConfig.autoCloseTranslate === true : legacyAutoClose;
            }

            if (autoTranslateSwitch) {
                autoTranslateSwitch.checked = uiConfig.autoTranslate === true; // 默认为false
            }

            if (autoModelSwitchSwitch) {
                autoModelSwitchSwitch.checked = uiConfig.autoModelSwitch === true; // 默认为false
            }

            if (autoCleanOcrSwitch) {
                autoCleanOcrSwitch.checked = uiConfig.autoCleanOCR !== false; // 默认为true
            }

            if (autoCleanTranslateSwitch) {
                autoCleanTranslateSwitch.checked = uiConfig.autoCleanTranslate !== false; // 默认为true
            }

            if (autoTranslateImageTranslateSwitch) {
                autoTranslateImageTranslateSwitch.checked = uiConfig.autoTranslateImageTranslate !== false; // 默认为true
            }

            if (autoCleanImageTranslateSwitch) {
                autoCleanImageTranslateSwitch.checked = uiConfig.autoCleanImageTranslate !== false; // 默认为true
            }

            // 加载显示使用统计开关状态
            if (showUsageStatsSwitch) {
                const personalSettings = JSON.parse(this.getStorageItem('personalSettings') || '{}');
                showUsageStatsSwitch.checked = personalSettings.showUsageStats !== false; // 默认为true
                this.toggleUsageStatsSection(showUsageStatsSwitch.checked);
            }

            // 加载默认模型配置（已移除功能）
        }
    }

    // 加载默认模型配置（已移除功能，保留结构）
    loadDefaultModelConfigs() {
        // 功能已移除，保留方法以维持兼容性
    }

    // 保存基础配置设置
    saveBaseConfigSetting(key, value) {
        if (window.ocrPlugin) {
            const config = window.ocrPlugin.config;
            if (!config.ui) {
                config.ui = {};
            }
            config.ui[key] = value;

            // 保存配置
            const result = window.ocrPlugin.configManager.saveConfig(config);
            if (result.success) {
                // 更新内存中的配置
                window.ocrPlugin.config = result.config;
                // 显示保存成功提示
                this.showNotification('设置已保存', 'success');
            } else {
                // 显示保存失败提示
                this.showNotification('设置保存失败', 'error');
            }
        }
    }

    // 保存模型服务配置设置
    saveModelServiceSetting(platform, key, value) {
        if (window.ocrPlugin) {
            const config = window.ocrPlugin.config;
            if (!config[platform]) {
                config[platform] = {};
            }

            // 记录旧值用于比较
            const oldValue = config[platform][key];
            config[platform][key] = value;

            // 只有在值真正改变时才保存和显示提示
            if (oldValue !== value) {
                // 保存配置
                const result = window.ocrPlugin.configManager.saveConfig(config);
                if (result.success) {
                    // 更新内存中的配置
                    window.ocrPlugin.config = result.config;
                    // 显示保存成功提示（更短的持续时间）
                    this.showNotification('配置已保存', 'success', 1200);
                } else {
                    // 显示保存失败提示
                    this.showNotification('配置保存失败', 'error');
                }
            }
        }
    }

    // 检查是否有未保存的配置更改
    hasUnsavedChanges() {
        if (!window.ocrPlugin || this.currentView !== 'config') {
            return false;
        }

        try {
            // 获取当前表单中的配置
            const currentFormConfigs = window.ocrPlugin.getAllAIPlatformConfigs();

            // 与保存的配置进行比较
            const savedConfig = window.ocrPlugin.config;

            for (const platform of Object.keys(currentFormConfigs)) {
                const formConfig = currentFormConfigs[platform];
                const savedPlatformConfig = savedConfig[platform] || {};

                // 比较API Key和Base URL
                if (formConfig.apiKey !== (savedPlatformConfig.apiKey || '') ||
                    formConfig.baseUrl !== (savedPlatformConfig.baseUrl || '')) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('检查未保存更改时出错:', error);
            return false;
        }
    }

    // 显示历史记录视图
    showHistoryView() {
        // 根据当前页面和配置决定是否清空内容
        const config = this.getOcrPlugin()?.configManager?.getConfig();
        if (this.currentView === 'translate' && config?.ui?.autoCleanTranslate) {
            this.clearTranslateContentSilently();
        } else if (this.currentView === 'main' && config?.ui?.autoCleanOCR) {
            this.clearOCRContentSilently();
        } else if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
            this.clearImageTranslateContentSilently();
        }

        // 如果当前在模型服务页面，保存选中的服务商
        this.saveCurrentSelectedModelService();

        // 清理图片翻译按钮控制事件
        this.cleanupImageTranslateButtonsControl();

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'none';
        document.getElementById('history-view').style.display = 'block';
        document.getElementById('translate-view').style.display = 'none';
        this.currentView = 'history';
        this.adjustLayout();

        // 更新导航栏按钮状态
        this.updateConfigNavButtons('history');

        // 加载历史记录
        if (window.ocrPlugin && window.ocrPlugin.historyManager) {
            window.ocrPlugin.historyManager.loadHistoryList();
        }

        // 记忆当前页面
        this.setStorageItem('lastConfigPage', 'history');
    }

    // 保存当前选中的模型服务商
    saveCurrentSelectedModelService() {
        // 只有在用户手动选择过服务商后才保存
        if (!this.hasUserSelectedModelService) {
            return;
        }

        // 检查当前是否在模型服务页面
        const modelServicePage = document.getElementById('model-service-page');
        const isInModelServicePage = modelServicePage && modelServicePage.style.display !== 'none';

        if (isInModelServicePage) {
            // 获取当前选中的服务商
            const activeServiceItem = document.querySelector('.service-item.active');

            if (activeServiceItem) {
                const selectedService = activeServiceItem.dataset.service;

                if (selectedService) {
                    this.setStorageItem('lastSelectedModelService', selectedService);
                }
            }
        }
    }

    // 显示翻译视图
    showTranslateView() {
        // 注意：不在这里清空OCR内容，因为需要先传递内容到翻译页面
        // OCR内容的清空将在fillTranslateInputFromOCR()方法中根据配置处理

        // 根据当前页面和配置决定是否清空图片翻译内容
        const config = this.getOcrPlugin()?.configManager?.getConfig();
        if (this.currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
            this.clearImageTranslateContentSilently();
        }

        // 如果当前在模型服务页面，保存选中的服务商
        this.saveCurrentSelectedModelService();

        // 清理图片翻译按钮控制事件
        this.cleanupImageTranslateButtonsControl();

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'none';
        document.getElementById('history-view').style.display = 'none';
        document.getElementById('image-translate-view').style.display = 'none';
        document.getElementById('translate-view').style.display = 'block';
        this.currentView = 'translate';
        this.adjustLayout();

        // 清除配置导航栏的高亮状态
        this.clearConfigNavButtons();

        // 如果有OCR识别结果，自动填入翻译输入框
        this.fillTranslateInputFromOCR();

        // 初始化翻译模型选择器
        this.initTranslateModelSelector();

        // 初始化语言选择器（只在第一次或需要时初始化）
        this.ensureLanguageSelectorsInitialized();

        // 初始化翻译页面的占位符
        this.initOriginalPlaceholders();

        // 绑定翻译按钮事件
        this.bindTranslateButtonEvents();

        // 绑定翻译页面其他事件
        this.bindTranslatePageEvents();

        // 绑定图片翻译按钮事件
        this.bindImageTranslateButtonEvent();

        // 重新设置翻译按钮控制
        this.setupTranslateButtonsControl();

        // 如果是通过翻译功能指令打开的，自动聚焦到输入框
        this.focusTranslateInputIfNeeded();

        // 更新翻译页面主题切换按钮图标
        const currentThemeMode = this.getThemeMode();
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        this.updateThemeIcon(currentTheme, currentThemeMode);

        // 初始化智能检测状态
        this.resetSmartDetection();

        // 确保翻译输入框获得焦点（延迟执行以确保DOM完全渲染）
        setTimeout(() => {
            this.focusTranslateInput();
        }, 100);
    }

    // 显示图片翻译视图
    showImageTranslateView() {
        // 如果当前在模型服务页面，保存选中的服务商
        this.saveCurrentSelectedModelService();

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'none';
        document.getElementById('history-view').style.display = 'none';
        document.getElementById('translate-view').style.display = 'none';
        document.getElementById('image-translate-view').style.display = 'block';
        this.currentView = 'image-translate';
        this.adjustLayout();

        // 清除配置导航栏的高亮状态
        this.clearConfigNavButtons();

        // 绑定图片翻译页面事件
        this.bindImageTranslatePageEvents();

        // 重新设置图片翻译按钮控制
        this.setupImageTranslateButtonsControl();

        // 确保图片翻译输入框获得焦点（延迟执行以确保DOM完全渲染）
        setTimeout(() => {
            this.focusImageTranslateInput();
        }, 100);
    }

    // 从OCR结果填充翻译输入框
    fillTranslateInputFromOCR() {
        const translateInput = document.getElementById('translate-source-text');
        if (!translateInput) {
            return;
        }

        // 优先检查是否有预设的翻译文本（来自文本匹配指令）
        let textToFill = '';
        if (window.pendingTranslateText) {
            textToFill = window.pendingTranslateText;
            // 使用后清除，避免影响后续操作
            delete window.pendingTranslateText;
        } else if (window.shouldFillFromOCR) {
            // 只有在明确标识允许从OCR获取数据时才执行
            // 获取当前OCR识别结果
            let ocrResult = '';

            // 优先使用当前文本框中的内容（包括用户编辑后的内容）
            const resultText = document.getElementById('result-text');
            const rawResultText = document.getElementById('raw-result-text');

            // 检查当前显示模式
            const singleContainer = document.getElementById('single-result-container');
            const dualContainer = document.getElementById('dual-result-container');

            const isSingleMode = singleContainer && singleContainer.style.display !== 'none';
            const isDualMode = dualContainer && dualContainer.style.display !== 'none';

            if (isSingleMode && resultText && resultText.value && resultText.value.trim()) {
                // 单栏模式：从result-text获取当前内容
                ocrResult = resultText.value.trim();
            } else if (isDualMode && rawResultText && rawResultText.value && rawResultText.value.trim()) {
                // 双栏模式：从raw-result-text获取当前内容
                ocrResult = rawResultText.value.trim();
            } else if (this.originalResultText && this.originalResultText.trim()) {
                // 如果文本框为空，才使用保存的原始识别结果作为备选
                ocrResult = this.originalResultText.trim();
            }

            textToFill = ocrResult;

            // 使用后立即清除标识，避免影响后续操作
            delete window.shouldFillFromOCR;
        }

        // 检查翻译输入框当前状态
        const currentInputValue = translateInput.value.trim();

        // 如果有文本要填入且翻译输入框为空，则自动填入
        if (textToFill && !currentInputValue) {
            translateInput.value = textToFill;

            // 触发输入事件，确保相关的UI更新
            translateInput.dispatchEvent(new Event('input', { bubbles: true }));

            // 内容传递完成后，根据配置决定是否清空OCR页面内容
            const config = window.ocrPlugin?.configManager?.getConfig();
            if (config?.ui?.autoCleanOCR) {
                // 延迟清空OCR内容，确保内容传递完成
                setTimeout(() => {
                    this.clearOCRContentSilently();
                }, 100);
            }

            // 检查是否启用自动翻译
            if (config?.ui?.autoTranslate) {
                // 如果启用了自动翻译，直接执行翻译而不聚焦输入框
                setTimeout(() => {
                    this.performAutoTranslation(textToFill);
                }, 200); // 稍微延迟以确保UI更新完成
            } else {
                // 如果没有启用自动翻译，将光标定位到输入框并聚焦
                setTimeout(() => {
                    translateInput.focus();
                    // 将光标定位到文本末尾
                    translateInput.setSelectionRange(translateInput.value.length, translateInput.value.length);
                }, 100);
            }

            // 标记已经处理了聚焦，避免重复聚焦
            this._translateInputFocused = true;
        }
    }

    // 聚焦翻译输入框
    focusTranslateInput() {
        // 如果已经处理过聚焦，跳过
        if (this._translateInputFocused) {
            this._translateInputFocused = false; // 重置标志
            return;
        }

        const translateInput = document.getElementById('translate-source-text');
        if (!translateInput) {
            return;
        }

        // 只有在输入框为空时才聚焦（避免干扰已有内容的情况）
        const currentInputValue = translateInput.value.trim();
        if (!currentInputValue) {
            try {
                translateInput.focus();
                // 将光标定位到输入框开始位置
                translateInput.setSelectionRange(0, 0);
            } catch (error) {
                console.error('聚焦翻译输入框失败:', error);
            }
        }
    }

    // 如果需要，聚焦翻译输入框（用于翻译功能指令）
    focusTranslateInputIfNeeded() {
        // 延迟执行，确保页面已完全加载
        setTimeout(() => {
            const translateInput = document.getElementById('translate-source-text');
            if (translateInput) {
                try {
                    translateInput.focus();
                    // 将光标定位到输入框末尾
                    const length = translateInput.value.length;
                    translateInput.setSelectionRange(length, length);
                } catch (error) {
                    console.error('聚焦翻译输入框失败:', error);
                }
            }
        }, 100);
    }

    // 渲染服务商列表（根据配置顺序）
    renderServiceList() {
        const serviceListContainer = document.querySelector('.service-list');
        if (!serviceListContainer) return;

        // 保存当前的状态指示器状态
        const currentStatuses = {};
        const existingItems = serviceListContainer.querySelectorAll('.service-item');
        existingItems.forEach(item => {
            const serviceKey = item.dataset.service;
            const statusIndicator = item.querySelector('.service-status-indicator');
            if (serviceKey && statusIndicator) {
                currentStatuses[serviceKey] = statusIndicator.dataset.status || 'unknown';
            }
        });

        // 获取服务商顺序配置
        const serviceOrder = window.ocrPlugin?.configManager?.getServiceOrder() || [
            'baidu', 'tencent', 'aliyun', 'openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'
        ];

        // 服务商信息映射
        const serviceInfo = {
            baidu: { name: '百度智能云', icon: 'baidu-icon' },
            tencent: { name: '腾讯云', icon: 'tencent-icon' },
            aliyun: { name: '阿里云', icon: 'aliyun-icon' },
            openai: { name: 'OpenAI', icon: 'openai-icon' },
            anthropic: { name: 'Anthropic', icon: 'anthropic-icon' },
            google: { name: 'Gemini', icon: 'google-icon' },
            alibaba: { name: '阿里云百炼', icon: 'alibaba-icon' },
            bytedance: { name: '火山引擎', icon: 'bytedance-icon' },
            ocrpro: { name: 'OCR Pro', icon: 'ocrpro-icon' },
            utools: { name: 'uTools AI', icon: 'utools-icon' }
        };

        // 获取当前选中的服务，优先使用存储的选中服务商
        const storedService = this.getStorageItem('lastSelectedModelService');
        const currentService = storedService || document.querySelector('.service-item.active')?.dataset?.service || 'baidu';

        // 生成服务商列表HTML
        const serviceListHtml = serviceOrder.map(serviceKey => {
            const service = serviceInfo[serviceKey];
            if (!service) return '';

            const isActive = serviceKey === currentService;
            const activeClass = isActive ? ' active' : '';

            // 获取服务商图标SVG
            const iconSvg = this.getServiceIcon(serviceKey);
            const iconHtml = iconSvg ? iconSvg : '';

            // 恢复之前的状态指示器状态，如果没有则使用unknown
            const statusValue = currentStatuses[serviceKey] || 'unknown';

            return `
                <div class="service-item${activeClass}" data-service="${serviceKey}">
                    <div class="service-icon">${iconHtml}</div>
                    <span class="service-name">${service.name}</span>
                    <div class="service-status-indicator" data-status="${statusValue}"></div>
                </div>
            `;
        }).join('');

        // 更新服务列表HTML
        serviceListContainer.innerHTML = serviceListHtml;

        // 重新绑定服务列表点击事件
        this.bindServiceListEvents();

        // 启用拖拽排序功能
        this.enableServiceListDragSort(serviceListContainer);
    }

    // 强制刷新服务商列表（用于处理新服务商添加后的缓存问题）
    forceRefreshServiceList() {
        // 清除可能的缓存
        if (window.ocrPlugin && window.ocrPlugin.configManager) {
            // 强制重新加载配置
            window.ocrPlugin.config = window.ocrPlugin.configManager.getConfig();
        }

        // 重新渲染服务商列表
        this.renderServiceList();

        // 更新所有服务的状态指示器
        if (window.ocrPlugin && window.ocrPlugin.updateAllServiceIndicators) {
            window.ocrPlugin.updateAllServiceIndicators();
        }
    }

    // 绑定服务列表事件（使用事件委托优化性能）
    bindServiceListEvents() {
        const serviceListContainer = document.querySelector('.service-list');
        if (!serviceListContainer) return;

        // 移除之前的事件监听器（如果有）
        if (serviceListContainer._boundClickHandler) {
            serviceListContainer.removeEventListener('click', serviceListContainer._boundClickHandler);
        }

        // 使用事件委托处理所有服务项点击
        const boundHandler = (event) => {
            const serviceItem = event.target.closest('.service-item');
            if (serviceItem) {
                // 创建一个模拟的事件对象，保持与原有逻辑兼容
                const mockEvent = {
                    currentTarget: serviceItem,
                    target: event.target,
                    preventDefault: () => event.preventDefault(),
                    stopPropagation: () => event.stopPropagation()
                };
                this.handleServiceItemClick.call(this, mockEvent);
            }
        };

        serviceListContainer._boundClickHandler = boundHandler;
        serviceListContainer.addEventListener('click', boundHandler);
    }

    // 启用服务商列表拖拽排序
    enableServiceListDragSort(listContainer) {
        if (!listContainer || !window.ocrPlugin) return;
        window.ocrPlugin.enableDragSort(listContainer, 'service');
    }

    // 处理服务项点击
    handleServiceItemClick(event) {
        const serviceItem = event.currentTarget;
        const serviceName = serviceItem.dataset.service;

        if (serviceName && window.ocrPlugin) {
            // 标记用户已经手动选择过服务商
            this.hasUserSelectedModelService = true;

            // 记录当前选中的服务商
            this.setStorageItem('lastSelectedModelService', serviceName);

            // 更新服务列表选中状态
            this.updateServiceListSelection(serviceName);

            // 更新隐藏的下拉框值（保持兼容性）
            const serviceSelect = document.getElementById('ocr-service');
            if (serviceSelect) {
                serviceSelect.value = serviceName;
            }

            // 只切换配置区域显示，不触发服务状态更新
            this.switchConfigSection(serviceName);

            // 如果是AI服务，加载对应的平台配置
            if (['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(serviceName)) {
                window.ocrPlugin.loadAIPlatformConfigs();
            }
        }
    }

    // 更新服务列表选中状态
    updateServiceListSelection(selectedService) {
        // 检查是否有存储的模型服务页面选择，如果有则优先使用
        const storedService = this.getStorageItem('lastSelectedModelService');

        if (storedService) {
            // 如果当前在模型服务页面，或者有存储的选择，都使用存储的服务商
            const modelServicePage = document.getElementById('model-service-page');
            const isInModelServicePage = modelServicePage && modelServicePage.style.display !== 'none';

            // 只要有存储的选择，就使用它（不管当前是否在模型服务页面）
            selectedService = storedService;

            // 如果在模型服务页面，同步更新配置区域显示
            if (isInModelServicePage) {
                this.switchConfigSection(storedService);

                // 同步更新下拉框
                const serviceSelect = document.getElementById('ocr-service');
                if (serviceSelect && serviceSelect.value !== storedService) {
                    serviceSelect.value = storedService;
                }
            }
        }

        const serviceItems = document.querySelectorAll('.service-item');
        if (serviceItems.length > 0) {
            serviceItems.forEach(item => {
                if (item.dataset.service === selectedService) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        } else {
            // 如果DOM元素还不存在，延迟执行
            setTimeout(() => {
                const delayedServiceItems = document.querySelectorAll('.service-item');
                delayedServiceItems.forEach(item => {
                    if (item.dataset.service === selectedService) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            }, 100);
        }
    }

    // 根据服务生成选择框的值
    generateServiceSelectValue(serviceName) {
        // 新的配置结构中，直接返回服务名称
        return serviceName;
    }

    // 显示加载状态
    showLoading(message = '正在处理...') {
        const loading = document.getElementById('loading');
        if (!loading) return;

        this.updateLoadingMessage(loading, message);
        loading.style.display = 'block';
        this.isLoading = true;

        // 检查是否是截图消息，设置截图状态
        if (message.includes('截图') || message.includes('按ESC键可取消')) {
            this.isCapturing = true;
        } else {
            // 如果是识别相关的加载状态，设置识别中占位符
            if (message.includes('识别') || message.includes('处理')) {
                this.setRecognizingPlaceholder();
            }
        }

        // 更新状态显示
        this.updateRecognitionStatus('processing', '识别中');
        // 禁用操作按钮
        this.setButtonsEnabled(false);
    }

    // 更新加载消息
    updateLoadingMessage(loading, message) {
        const loadingTitle = loading.querySelector('.loading-title');
        const loadingDesc = loading.querySelector('.loading-desc');

        if (loadingTitle && loadingDesc) {
            this.updateNewLayoutMessage(loadingTitle, loadingDesc, message);
        } else {
            this.updateLegacyLayoutMessage(loading, message);
        }
    }

    // 更新新布局的消息
    updateNewLayoutMessage(titleEl, descEl, message) {
        if (message.includes('<br>')) {
            const parts = message.split('<br>');
            titleEl.textContent = parts[0];
            descEl.innerHTML = parts[1] || '';
        } else {
            titleEl.textContent = message;
            descEl.textContent = '请稍候...';
        }
    }

    // 更新旧布局的消息
    updateLegacyLayoutMessage(loading, message) {
        const messageElement = loading.querySelector('p');
        if (messageElement) {
            if (message.includes('<')) {
                messageElement.innerHTML = message;
            } else {
                messageElement.textContent = message;
            }
        }
    }

    // 隐藏加载状态
    hideLoading() {
        try {
            const loadingElement = document.getElementById('loading');
            if (loadingElement && loadingElement.style) {
                loadingElement.style.display = 'none';
            }
            this.isLoading = false;
            this.isCapturing = false; // 重置截图状态

            // 恢复默认占位符（只有在没有显示结果的情况下）
            if (!this.originalResultText) {
                this.restoreDefaultPlaceholder();
                this.restoreServiceStatus();
            }

            // 启用操作按钮
            this.setButtonsEnabled(true);
        } catch (error) {
            console.error('隐藏加载状态时出错:', error);
            // 确保状态被重置
            this.isLoading = false;
            this.isCapturing = false; // 重置截图状态
            this.setButtonsEnabled(true);
            // 强制恢复服务状态和占位符
            this.restoreDefaultPlaceholder();
            this.restoreServiceStatus();
        }
    }

    // 设置按钮启用状态（只影响OCR相关按钮，不影响翻译页面按钮）
    setButtonsEnabled(enabled) {
        // 只禁用OCR相关的按钮，不影响翻译页面的按钮
        const ocrButtons = [
            'capture-btn',
            'paste-btn',
            'file-btn',
            'clear-btn',
            're-recognize-btn',
            'copy-btn',
            'linebreak-toggle-btn'
        ];

        ocrButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = !enabled;
            }
        });

        // 也禁用模式切换按钮
        const modeButtons = document.querySelectorAll('.mode-btn');
        modeButtons.forEach(btn => {
            btn.disabled = !enabled;
        });

        // 禁用模型选择下拉菜单
        const modelSelect = document.getElementById('model-select');
        if (modelSelect) {
            modelSelect.disabled = !enabled;
        }
    }

    // 检查是否有可用模型
    hasAvailableModels() {
        try {
            const ocrPlugin = this.getOcrPlugin();
            if (ocrPlugin && ocrPlugin.getAvailableServicesForMainInterface) {
                const availableServices = ocrPlugin.getAvailableServicesForMainInterface();
                return availableServices && availableServices.length > 0;
            }
            return false;
        } catch (error) {
            console.warn('检查可用模型时出错:', error);
            return false;
        }
    }

    // 更新当前服务状态
    updateCurrentService(serviceName, model = null) {
        const currentServiceEl = document.getElementById('current-service');
        if (currentServiceEl) {
            let displayName;

            // 如果没有配置服务，显示提示文本
            if (!serviceName) {
                displayName = '请先配置模型';
            } else {
                // 有服务配置，检查是否有模型配置
                const ocrPlugin = this.getOcrPlugin();
                if (ocrPlugin) {
                    // 优先检查识别模式配置
                    const currentMode = ocrPlugin.getCurrentRecognitionMode();
                    const modeConfig = ocrPlugin.configManager.getRecognitionModeConfig(currentMode);

                    let hasValidConfig = false;

                    if (modeConfig && modeConfig.service && modeConfig.model) {
                        // 识别模式有完整配置
                        hasValidConfig = true;
                        displayName = this.getServiceShortName(modeConfig.service, modeConfig.model);
                    } else if (serviceName && model) {
                        // 传入了服务和模型参数
                        hasValidConfig = true;
                        displayName = this.getServiceShortName(serviceName, model);
                    } else {
                        // 检查全局配置
                        const serviceConfig = ocrPlugin.config[serviceName];
                        if (serviceConfig && serviceConfig.model) {
                            hasValidConfig = true;
                            displayName = this.getServiceShortName(serviceName, serviceConfig.model);
                        }
                    }

                    if (!hasValidConfig) {
                        displayName = '请先配置模型';
                    }
                } else {
                    // 无法获取插件实例，使用基本逻辑
                    displayName = this.getServiceShortName(serviceName, model);
                }
            }

            const serviceNameEl = currentServiceEl.querySelector('.service-name');
            if (serviceNameEl) {
                serviceNameEl.textContent = displayName;
            } else {
                // 兼容旧版本HTML结构
                currentServiceEl.textContent = displayName;
            }
        }
    }

    // 更新识别状态
    updateRecognitionStatus(status, text) {
        try {
            const statusEl = document.getElementById('recognition-status');
            if (statusEl && statusEl.textContent !== undefined) {
                statusEl.textContent = text || status;
                statusEl.className = `status-value ${status}`;
            }

            // 注意：不在这里更新配置页面的状态指示器，避免与模型测试状态冲突
            // 配置页面的状态指示器应该由专门的状态管理逻辑来更新
        } catch (error) {
            console.error('更新识别状态时发生错误:', error);
        }
    }

    // 恢复服务的真实连接状态
    restoreServiceStatus() {
        if (!window.ocrPlugin) {
            this.updateRecognitionStatus('ready', '就绪');
            return;
        }

        // 使用新的统一状态管理系统
        window.ocrPlugin.updateMainPageStatus();
    }

    // 更新配置页面服务状态指示器
    updateConfigServiceStatus(serviceName, status) {
        if (!serviceName) return;

        try {
            const serviceItem = document.querySelector(`.service-item[data-service="${serviceName}"]`);
            if (serviceItem) {
                const indicator = serviceItem.querySelector('.service-status-indicator');
                if (indicator) {
                    // 获取当前状态，避免不必要的更新
                    const currentStatus = indicator.getAttribute('data-status');

                    // 映射状态类型到指示器状态
                    let indicatorStatus = 'unknown';
                    if (status === 'ready' || status === 'success') {
                        indicatorStatus = 'ready'; // 绿色 - 配置成功
                    } else if (status === 'error') {
                        indicatorStatus = 'error'; // 红色 - 连接失败
                    } else if (status === 'unconfigured') {
                        indicatorStatus = 'unconfigured'; // 灰色 - 未配置
                    } else if (status === 'processing') {
                        indicatorStatus = 'unknown'; // 灰色 - 处理中
                    } else {
                        indicatorStatus = 'unknown'; // 灰色 - 其他状态
                    }

                    // 只有状态真正发生变化时才更新
                    if (currentStatus !== indicatorStatus) {
                        // 如果状态从ready变为unconfigured，记录详细信息
                        if (currentStatus === 'ready' && indicatorStatus === 'unconfigured') {

                            // 触发配置检查
                            if (window.ocrPlugin) {
                                setTimeout(() => {
                                    window.ocrPlugin.isServiceConfigured(serviceName);
                                }, 100);
                            }
                        }

                        indicator.setAttribute('data-status', indicatorStatus);
                    }
                }
            }
        } catch (error) {
            console.error('更新配置页面服务状态指示器时发生错误:', error);
        }
    }

    // 更新所有服务的状态指示器
    updateAllServiceStatusIndicators() {
        if (!window.ocrPlugin) return;

        // 直接调用主插件的updateAllServiceIndicators方法
        window.ocrPlugin.updateAllServiceIndicators();
    }

    // 显示图片预览
    showImagePreview(imageBase64) {
        const imagePreview = document.getElementById('image-preview');
        const previewImg = document.getElementById('preview-img');
        const placeholder = document.getElementById('preview-placeholder');

        if (imagePreview && previewImg && placeholder) {
            previewImg.src = imageBase64;
            imagePreview.style.display = 'flex';
            placeholder.style.display = 'none';

            // 初始化图片缩放功能
            this.initImageZoom();
        }
    }

    // 初始化图片缩放功能
    initImageZoom() {
        // 如果已经初始化过，先销毁之前的实例
        if (this.imageZoomManager) {
            this.imageZoomManager.destroy();
            this.imageZoomManager = null;
        }

        // 等待图片加载完成后再初始化缩放功能
        const previewImg = document.getElementById('preview-img');
        if (previewImg && previewImg.src) {
            // 如果图片已经加载完成
            if (previewImg.complete) {
                this.createImageZoomManager();
            } else {
                // 等待图片加载完成
                previewImg.onload = () => {
                    this.createImageZoomManager();
                };
            }
        }
    }

    // 创建图片缩放管理器
    createImageZoomManager() {
        try {
            // 使用图片预览区域作为容器，预览图片作为目标
            this.imageZoomManager = new ImageZoomManager('#image-preview', '#preview-img');
        } catch (error) {
            console.error('初始化图片缩放功能失败:', error);
        }
    }

    // 初始化图片翻译输入区域的缩放功能
    initImageTranslateInputZoom() {
        // 如果已经初始化过，先销毁之前的实例
        if (this.imageTranslateInputZoomManager) {
            this.imageTranslateInputZoomManager.destroy();
            this.imageTranslateInputZoomManager = null;
        }

        // 等待图片加载完成后再初始化缩放功能
        const previewImg = document.getElementById('image-translate-preview-img');
        if (previewImg && previewImg.src) {
            // 如果图片已经加载完成
            if (previewImg.complete) {
                this.createImageTranslateInputZoomManager();
            } else {
                // 等待图片加载完成
                previewImg.onload = () => {
                    this.createImageTranslateInputZoomManager();
                };
            }
        }
    }

    // 创建图片翻译输入区域的缩放管理器
    createImageTranslateInputZoomManager() {
        try {
            // 使用图片翻译预览区域作为容器，预览图片作为目标
            this.imageTranslateInputZoomManager = new ImageZoomManager('#image-translate-preview', '#image-translate-preview-img');
        } catch (error) {
            console.error('初始化图片翻译输入缩放功能失败:', error);
        }
    }

    // 初始化图片翻译结果区域的缩放功能
    initImageTranslateResultZoom() {
        // 如果已经初始化过，先销毁之前的实例
        if (this.imageTranslateResultZoomManager) {
            this.imageTranslateResultZoomManager.destroy();
            this.imageTranslateResultZoomManager = null;
        }

        // 等待图片加载完成后再初始化缩放功能
        const resultImg = document.getElementById('image-translate-result-img');
        if (resultImg && resultImg.src) {
            // 如果图片已经加载完成
            if (resultImg.complete) {
                this.createImageTranslateResultZoomManager();
            } else {
                // 等待图片加载完成
                resultImg.onload = () => {
                    this.createImageTranslateResultZoomManager();
                };
            }
        }
    }

    // 创建图片翻译结果区域的缩放管理器
    createImageTranslateResultZoomManager() {
        try {
            // 使用图片翻译结果预览区域作为容器，结果图片作为目标
            this.imageTranslateResultZoomManager = new ImageZoomManager('#image-translate-result-preview', '#image-translate-result-img');
        } catch (error) {
            console.error('初始化图片翻译结果缩放功能失败:', error);
        }
    }

    // 隐藏图片预览
    hideImagePreview() {
        const imagePreview = document.getElementById('image-preview');
        const placeholder = document.getElementById('preview-placeholder');

        if (imagePreview && placeholder) {
            imagePreview.style.display = 'none';
            placeholder.style.display = 'flex';
        }

        // 销毁缩放管理器
        if (this.imageZoomManager) {
            this.imageZoomManager.destroy();
            this.imageZoomManager = null;
        }
    }

    // 清空图片预览
    clearImagePreview() {
        const previewImg = document.getElementById('preview-img');
        if (previewImg) {
            // 移除 src 属性而不是设置为空字符串，避免资源加载错误
            previewImg.removeAttribute('src');
        }

        // 销毁缩放管理器
        if (this.imageZoomManager) {
            this.imageZoomManager.destroy();
            this.imageZoomManager = null;
        }

        this.hideImagePreview();
    }

    // 显示结果
    async showResult(text, confidence = null, mode = 'text') {
        try {
            const resultContainer = document.getElementById('result-container');
            const linebreakToggleBtn = document.getElementById('linebreak-toggle-btn');

            // 保存原始文本
            this.originalResultText = text;

            // 处理换行符
            let processedText = text;
            if (linebreakToggleBtn && linebreakToggleBtn.getAttribute('data-enabled') === 'false') {
                processedText = text.replace(/\n+/g, '').replace(/\s+/g, ' ').trim();
            }

            // 根据识别模式选择显示方式
            if (mode === 'text') {
                this.showSingleColumnResult(processedText);
            } else {
                await this.showDualColumnResult(processedText, mode);
            }

            // 恢复原始占位符
            this.restoreDefaultPlaceholder();

            // 安全地设置容器显示状态
            if (resultContainer) {
                resultContainer.style.display = 'block';
            }

            // 如果有置信度信息，显示
            if (confidence !== null) {
                this.showConfidence(confidence);
            }

            // 更新状态显示为成功
            this.updateRecognitionStatus('success', '识别完成');

            // 显示识别成功消息
            this.showRecognitionSuccess(confidence !== null, confidence);

            // 滚动到结果区域
            if (resultContainer) {
                resultContainer.scrollIntoView({ behavior: 'smooth' });
            }

            // 自动复制（如果配置了）
            const config = window.configManager?.getConfig();
            if (config?.ui?.copyAfterOCR) {
                window.ocrAPI?.copyText?.(processedText);
                this.showSuccess('已自动复制到剪贴板');

                // 注意：自动复制不触发自动关闭功能，避免与用户手动复制的自动关闭冲突
            }
        } catch (error) {
            console.error('🔴 显示结果时发生错误:', {
                message: error.message,
                stack: error.stack,
                error: error,
                text: text,
                mode: mode
            });
            // 不要显示错误提示，因为识别实际上是成功的
        }
    }

    // 显示单栏结果（文字模式）
    showSingleColumnResult(text) {
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');
        const resultText = document.getElementById('result-text');

        if (!resultText) {
            console.error('result-text元素不存在');
            return;
        }

        // 显示单栏，隐藏双栏
        if (singleContainer) singleContainer.style.display = 'flex';
        if (dualContainer) dualContainer.style.display = 'none';

        // 设置文本内容
        resultText.value = text;
    }

    // 显示单栏布局（仅布局切换，不设置内容）
    showSingleColumnLayout() {
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');

        // 显示单栏，隐藏双栏
        if (singleContainer) singleContainer.style.display = 'flex';
        if (dualContainer) dualContainer.style.display = 'none';
    }

    // 显示双栏布局（可选择是否为空白状态）
    showDualColumnLayout(mode, isEmptyState = false) {
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');
        const renderedResult = document.getElementById('rendered-result');
        const rawResultText = document.getElementById('raw-result-text');

        if (!renderedResult || !rawResultText) {
            console.error('双栏结果显示元素不存在');
            return;
        }

        // 隐藏单栏，显示双栏
        if (singleContainer) singleContainer.style.display = 'none';
        if (dualContainer) dualContainer.style.display = 'flex';

        if (isEmptyState) {
            // 空白状态：清空内容，设置占位符
            renderedResult.innerHTML = this.getEmptyStateContent(mode);
            rawResultText.value = '';
            rawResultText.placeholder = this.getEmptyStatePlaceholder(mode);

            // 隐藏操作按钮
            const actionButtonsContainer = document.getElementById('dual-action-buttons');
            if (actionButtonsContainer) {
                actionButtonsContainer.style.display = 'none';
            }

            // 即使在空白状态下也绑定实时编辑事件，支持纯编辑器模式
            this.bindRawResultEditEvent(rawResultText, renderedResult, mode);
        } else {
            // 创建和显示操作按钮
            this.createDualActionButtons(mode);

            // 绑定原始结果编辑事件，实现实时更新渲染结果
            this.bindRawResultEditEvent(rawResultText, renderedResult, mode);
        }
    }

    // 获取空白状态的占位内容
    getEmptyStateContent(mode) {
        // 返回空字符串，不显示任何占位文字
        return '';
    }

    // 获取空白状态的输入框占位符
    getEmptyStatePlaceholder(mode) {
        const placeholders = {
            'table': '在此输入表格代码或截图进行OCR识别...',
            'formula': '在此输入LaTeX公式或截图进行OCR识别...',
            'markdown': '在此输入Markdown文本或截图进行OCR识别...'
        };
        return placeholders[mode] || '在此输入内容或截图进行OCR识别...';
    }

    // 显示双栏结果（公式、表格、Markdown模式）
    async showDualColumnResult(text, mode) {
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');
        const renderedResult = document.getElementById('rendered-result');
        const rawResultText = document.getElementById('raw-result-text');

        if (!renderedResult || !rawResultText) {
            console.error('双栏结果显示元素不存在');
            return;
        }

        // 确保双栏布局已显示
        if (singleContainer) singleContainer.style.display = 'none';
        if (dualContainer) dualContainer.style.display = 'flex';

        // 设置原始结果
        rawResultText.value = text;
        // 清除空白状态的占位符
        rawResultText.placeholder = '原始识别结果将显示在这里，您可以直接编辑...';

        // 渲染结果
        await this.renderResult(text, mode, renderedResult);

        // 创建和显示操作按钮
        this.createDualActionButtons(mode);

        // 绑定原始结果编辑事件，实现实时更新渲染结果
        this.bindRawResultEditEvent(rawResultText, renderedResult, mode);
    }

    // 创建双栏操作按钮
    createDualActionButtons(mode) {
        const actionButtonsContainer = document.getElementById('dual-action-buttons');
        if (!actionButtonsContainer) {
            return;
        }

        // 清空现有按钮
        actionButtonsContainer.innerHTML = '';

        // 根据模式显示不同的按钮
        if (mode === 'table') {
            // 显示按钮容器
            actionButtonsContainer.style.display = 'flex';

            // 创建"复制Markdown"按钮
            const copyMarkdownBtn = this.createActionButton(
                'copy-markdown-btn',
                '复制Markdown',
                this.getCopyIcon(),
                () => this.copyTableAsMarkdown()
            );

            // 创建"复制表格"按钮
            const copyTableBtn = this.createActionButton(
                'copy-table-btn',
                '复制表格',
                this.getTableIcon(),
                () => this.copyTableAsTabSeparated()
            );

            actionButtonsContainer.appendChild(copyMarkdownBtn);
            actionButtonsContainer.appendChild(copyTableBtn);
        } else if (mode === 'formula') {
            // 显示按钮容器
            actionButtonsContainer.style.display = 'flex';

            // 创建"复制LaTeX"按钮
            const copyLatexBtn = this.createActionButton(
                'copy-latex-btn',
                '复制LaTeX',
                this.getCopyIcon(),
                () => this.copyFormulaAsLatex()
            );

            // 创建"导出图片"按钮
            const exportImageBtn = this.createActionButton(
                'export-image-btn',
                '导出图片',
                this.getImageIcon(),
                () => this.exportFormulaAsImage()
            );

            actionButtonsContainer.appendChild(copyLatexBtn);
            actionButtonsContainer.appendChild(exportImageBtn);
        } else {
            // 其他模式隐藏按钮容器
            actionButtonsContainer.style.display = 'none';
        }
    }

    // 创建操作按钮的通用方法
    createActionButton(id, text, iconSvg, clickHandler) {
        const button = document.createElement('button');
        button.id = id;
        button.className = 'control-btn';
        button.innerHTML = `
            <div class="btn-icon">${iconSvg}</div>
            <span class="btn-text">${text}</span>
        `;
        button.addEventListener('click', clickHandler);
        return button;
    }

    // 统一图标管理
    getIcon(iconName) {
        const icons = {
            copy: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>`,
            table: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h4m6 0h10"></path>
            </svg>`,
            image: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="9" cy="9" r="2"></circle>
                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
            </svg>`
        };
        return icons[iconName] || '';
    }

    // 向后兼容的方法
    getCopyIcon() { return this.getIcon('copy'); }
    getTableIcon() { return this.getIcon('table'); }
    getImageIcon() { return this.getIcon('image'); }

    // 复制表格为Markdown格式
    async copyTableAsMarkdown() {
        try {
            const rawResultText = document.getElementById('raw-result-text');
            if (!rawResultText || !rawResultText.value.trim()) {
                this.showNotification('没有可复制的表格内容', 'warning');
                return;
            }

            const markdownText = rawResultText.value.trim();
            await navigator.clipboard.writeText(markdownText);
            this.showNotification('Markdown表格已复制到剪贴板', 'success');

            // 检查是否启用OCR页面复制后自动关闭插件
            const ocrPlugin = this.getOcrPlugin();
            if (ocrPlugin) {
                const config = ocrPlugin.config;
                // 优先使用新配置，如果没有则使用旧配置（向后兼容）
                const shouldAutoClose = config?.ui?.autoCloseOCR !== undefined ?
                    config.ui.autoCloseOCR === true :
                    config?.ui?.autoClose === true;
                if (shouldAutoClose) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 300);
                }
            }
        } catch (error) {
            console.error('复制Markdown表格失败:', error);
            this.showNotification('复制失败，请重试', 'error');
        }
    }

    // 复制表格为制表符分隔格式（Excel兼容）
    async copyTableAsTabSeparated() {
        try {
            const rawResultText = document.getElementById('raw-result-text');
            if (!rawResultText || !rawResultText.value.trim()) {
                this.showNotification('没有可复制的表格内容', 'warning');
                return;
            }

            const markdownText = rawResultText.value.trim();
            const tabSeparatedText = this.convertMarkdownTableToTabSeparated(markdownText);

            if (!tabSeparatedText) {
                this.showNotification('表格格式无效，无法转换', 'warning');
                return;
            }

            await navigator.clipboard.writeText(tabSeparatedText);
            this.showNotification('表格已复制到剪贴板（Excel格式）', 'success');

            // 检查是否启用OCR页面复制后自动关闭插件
            const ocrPlugin = this.getOcrPlugin();
            if (ocrPlugin) {
                const config = ocrPlugin.config;
                // 优先使用新配置，如果没有则使用旧配置（向后兼容）
                const shouldAutoClose = config?.ui?.autoCloseOCR !== undefined ?
                    config.ui.autoCloseOCR === true :
                    config?.ui?.autoClose === true;
                if (shouldAutoClose) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 300);
                }
            }
        } catch (error) {
            console.error('复制表格失败:', error);
            this.showNotification('复制失败，请重试', 'error');
        }
    }

    // 将Markdown表格转换为制表符分隔格式
    convertMarkdownTableToTabSeparated(markdownText) {
        try {
            const lines = markdownText.trim().split('\n');
            if (lines.length < 2) {
                return null;
            }

            const result = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                // 跳过空行
                if (!line) continue;

                // 检查是否是分隔行（包含 - 和 | 的行）
                if (line.includes('-') && line.includes('|')) {
                    const separatorPattern = /^\|?[\s\-\|:]+\|?$/;
                    if (separatorPattern.test(line)) {
                        continue; // 跳过分隔行
                    }
                }

                // 处理表格行
                if (line.includes('|')) {
                    // 移除行首尾的 | 符号，然后按 | 分割
                    const cells = line.replace(/^\||\|$/g, '').split('|').map(cell => cell.trim());
                    if (cells.length > 0 && cells.some(cell => cell)) {
                        result.push(cells.join('\t'));
                    }
                }
            }

            return result.length > 0 ? result.join('\n') : null;
        } catch (error) {
            console.error('转换表格格式失败:', error);
            return null;
        }
    }

    // 复制公式为LaTeX格式
    async copyFormulaAsLatex() {
        try {
            const rawResultText = document.getElementById('raw-result-text');
            if (!rawResultText || !rawResultText.value.trim()) {
                this.showNotification('没有可复制的LaTeX内容', 'warning');
                return;
            }

            const latexText = rawResultText.value.trim();
            await navigator.clipboard.writeText(latexText);
            this.showNotification('LaTeX代码已复制到剪贴板', 'success');

            // 检查是否启用OCR页面复制后自动关闭插件
            const ocrPlugin = this.getOcrPlugin();
            if (ocrPlugin) {
                const config = ocrPlugin.config;
                // 优先使用新配置，如果没有则使用旧配置（向后兼容）
                const shouldAutoClose = config?.ui?.autoCloseOCR !== undefined ?
                    config.ui.autoCloseOCR === true :
                    config?.ui?.autoClose === true;
                if (shouldAutoClose) {
                    // 延迟一点时间让用户看到复制成功的提示
                    setTimeout(() => {
                        window.ocrAPI?.hideMainWindow?.();
                    }, 300);
                }
            }
        } catch (error) {
            console.error('复制LaTeX代码失败:', error);
            this.showNotification('复制失败，请重试', 'error');
        }
    }

    // 导出公式为图片
    async exportFormulaAsImage() {
        try {
            const renderedResult = document.getElementById('rendered-result');
            if (!renderedResult) {
                this.showNotification('没有可导出的公式内容', 'warning');
                return;
            }

            // 查找渲染的公式元素
            const formulaElements = renderedResult.querySelectorAll('.katex, .katex-display, .formula-display');
            if (formulaElements.length === 0) {
                this.showNotification('没有找到已渲染的公式', 'warning');
                return;
            }

            // 只渲染公式部分，不包括整个区域
            await this.convertFormulaElementsToImage(formulaElements);
        } catch (error) {
            console.error('导出公式图片失败:', error);
            this.showNotification('导出失败，请重试', 'error');
        }
    }

    // 将公式元素转换为图片并复制到剪贴板
    async convertFormulaElementsToImage(formulaElements) {
        try {
            // 使用更简单直接的方法：直接对现有元素进行渲染
            const canvas = await this.renderFormulasDirectly(formulaElements);

            // 将canvas转换为blob并复制到剪贴板
            canvas.toBlob(async (blob) => {
                try {
                    await navigator.clipboard.write([
                        new ClipboardItem({ 'image/png': blob })
                    ]);
                    this.showNotification('公式图片已复制到剪贴板', 'success');
                } catch (clipboardError) {
                    console.error('复制图片到剪贴板失败:', clipboardError);
                    this.showNotification('复制图片失败，请重试', 'error');
                }
            }, 'image/png');
        } catch (error) {
            console.error('导出公式图片失败:', error);
            this.showNotification('导出失败，请重试', 'error');
        }
    }

    // 直接渲染公式元素
    async renderFormulasDirectly(formulaElements) {
        if (typeof html2canvas === 'undefined') {
            throw new Error('html2canvas库未加载，可能是uTools插件环境限制导致外部资源加载失败');
        }

        // 找到包含所有公式的最小区域
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        formulaElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            minX = Math.min(minX, rect.left);
            minY = Math.min(minY, rect.top);
            maxX = Math.max(maxX, rect.right);
            maxY = Math.max(maxY, rect.bottom);
        });

        // 计算实际需要的尺寸
        const width = Math.ceil(maxX - minX);
        const height = Math.ceil(maxY - minY);

        // 获取渲染结果容器
        const renderedResult = document.getElementById('rendered-result');

        // 直接对渲染结果容器进行截图，但指定精确的区域
        const canvas = await html2canvas(renderedResult, {
            backgroundColor: '#ffffff',
            scale: 3,
            useCORS: true,
            allowTaint: true,
            x: minX - renderedResult.getBoundingClientRect().left,
            y: minY - renderedResult.getBoundingClientRect().top,
            width: width,
            height: height
        });

        return canvas;
    }

    // 渲染结果内容
    async renderResult(text, mode, container) {
        try {
            let renderedContent = '';

            switch (mode) {
                case 'formula':
                    renderedContent = await this.renderFormula(text);
                    break;
                case 'table':
                    renderedContent = this.renderTable(text);
                    break;
                case 'markdown':
                    renderedContent = this.renderMarkdown(text);
                    break;
                default:
                    renderedContent = `<div class="render-error">
                        <div class="render-error-title">不支持的渲染模式</div>
                        <div>模式: ${mode}</div>
                    </div>`;
            }

            container.innerHTML = renderedContent;
        } catch (error) {
            console.error('渲染结果时出错:', error);
            container.innerHTML = `<div class="render-error">
                <div class="render-error-title">渲染失败</div>
                <div>${error.message}</div>
            </div>`;
        }
    }

    // 渲染Markdown表格
    renderMarkdownTables(text) {
        const lines = text.split('\n');
        const result = [];
        let i = 0;

        while (i < lines.length) {
            const line = lines[i];

            // 检查是否是表格行（包含 | 分隔符）
            if (line.trim().includes('|') && line.trim().length > 1) {
                const tableLines = [];
                let j = i;

                // 收集连续的表格行
                while (j < lines.length && lines[j].trim().includes('|') && lines[j].trim().length > 1) {
                    tableLines.push(lines[j]);
                    j++;
                }

                if (tableLines.length >= 2) {
                    // 检查第二行是否是分隔符行
                    const separatorLine = tableLines[1].trim();
                    if (separatorLine.match(/^\|?[\s\-\|:]+\|?$/)) {
                        // 这是一个有效的Markdown表格
                        const tableHtml = this.convertMarkdownTableToHtml(tableLines);
                        result.push(tableHtml);
                        i = j;
                        continue;
                    }
                }
            }

            result.push(line);
            i++;
        }

        return result.join('\n');
    }

    // 将Markdown表格转换为HTML
    convertMarkdownTableToHtml(tableLines) {
        if (tableLines.length < 2) return tableLines.join('\n');

        const headerLine = tableLines[0];
        const separatorLine = tableLines[1];
        const dataLines = tableLines.slice(2);

        // 解析表头
        const headers = this.parseTableRow(headerLine);
        if (headers.length === 0) return tableLines.join('\n');

        // 解析对齐方式
        const alignments = this.parseTableAlignment(separatorLine, headers.length);

        let tableHtml = '<table>\n<thead>\n<tr>\n';
        headers.forEach((header, index) => {
            const align = alignments[index] ? ` style="text-align: ${alignments[index]}"` : '';
            tableHtml += `<th${align}>${header.trim()}</th>\n`;
        });
        tableHtml += '</tr>\n</thead>\n';

        if (dataLines.length > 0) {
            tableHtml += '<tbody>\n';
            dataLines.forEach(line => {
                const cells = this.parseTableRow(line);
                if (cells.length > 0) {
                    tableHtml += '<tr>\n';
                    cells.forEach((cell, index) => {
                        const align = alignments[index] ? ` style="text-align: ${alignments[index]}"` : '';
                        tableHtml += `<td${align}>${cell.trim()}</td>\n`;
                    });
                    tableHtml += '</tr>\n';
                }
            });
            tableHtml += '</tbody>\n';
        }

        tableHtml += '</table>';
        return tableHtml;
    }

    // 解析表格行
    parseTableRow(line) {
        const trimmed = line.trim();
        if (!trimmed.includes('|')) return [];

        // 移除首尾的 | 符号
        let content = trimmed;
        if (content.startsWith('|')) content = content.substring(1);
        if (content.endsWith('|')) content = content.substring(0, content.length - 1);

        // 分割单元格，但要处理转义的 |
        const cells = [];
        let currentCell = '';
        let escaped = false;

        for (let i = 0; i < content.length; i++) {
            const char = content[i];
            if (char === '\\' && !escaped) {
                escaped = true;
                continue;
            }
            if (char === '|' && !escaped) {
                cells.push(currentCell);
                currentCell = '';
            } else {
                currentCell += char;
            }
            escaped = false;
        }
        cells.push(currentCell);

        return cells.map(cell => cell.trim());
    }

    // 解析表格对齐方式
    parseTableAlignment(separatorLine, columnCount) {
        const cells = this.parseTableRow(separatorLine);
        const alignments = [];

        for (let i = 0; i < columnCount; i++) {
            const cell = cells[i] || '';
            const trimmed = cell.trim();

            if (trimmed.startsWith(':') && trimmed.endsWith(':')) {
                alignments[i] = 'center';
            } else if (trimmed.endsWith(':')) {
                alignments[i] = 'right';
            } else if (trimmed.startsWith(':')) {
                alignments[i] = 'left';
            } else {
                alignments[i] = null;
            }
        }

        return alignments;
    }

    // 渲染Markdown列表
    renderMarkdownLists(text) {
        const lines = text.split('\n');
        const result = [];
        let i = 0;

        while (i < lines.length) {
            const line = lines[i];

            // 检查是否是列表项
            const unorderedMatch = line.match(/^(\s*)([\*\-\+])\s+(.+)$/);
            const orderedMatch = line.match(/^(\s*)(\d+\.)\s+(.+)$/);

            if (unorderedMatch || orderedMatch) {
                const listItems = [];
                let j = i;
                let currentListType = unorderedMatch ? 'ul' : 'ol';

                // 收集连续的列表项
                while (j < lines.length) {
                    const currentLine = lines[j];
                    const currentUnordered = currentLine.match(/^(\s*)([\*\-\+])\s+(.+)$/);
                    const currentOrdered = currentLine.match(/^(\s*)(\d+\.)\s+(.+)$/);

                    if (currentUnordered && currentListType === 'ul') {
                        listItems.push({
                            indent: currentUnordered[1].length,
                            content: currentUnordered[3],
                            type: 'ul'
                        });
                        j++;
                    } else if (currentOrdered && currentListType === 'ol') {
                        listItems.push({
                            indent: currentOrdered[1].length,
                            content: currentOrdered[3],
                            type: 'ol'
                        });
                        j++;
                    } else if (currentLine.trim() === '') {
                        // 空行，继续检查下一行
                        j++;
                    } else {
                        // 不是列表项，结束收集
                        break;
                    }
                }

                if (listItems.length > 0) {
                    const listHtml = this.convertListItemsToHtml(listItems);
                    result.push(listHtml);
                    i = j;
                    continue;
                }
            }

            result.push(line);
            i++;
        }

        return result.join('\n');
    }

    // 将列表项转换为HTML
    convertListItemsToHtml(listItems) {
        if (listItems.length === 0) return '';

        const listType = listItems[0].type;
        let html = `<${listType}>\n`;

        listItems.forEach(item => {
            html += `<li>${item.content}</li>\n`;
        });

        html += `</${listType}>`;
        return html;
    }

    // 渲染数学公式
    renderMathFormulas(text) {
        // 块级公式 $$...$$
        text = text.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
            return `<div class="math-block">$$${formula.trim()}$$</div>`;
        });

        // 行内公式 $...$
        text = text.replace(/\$([^$\n]+)\$/g, (match, formula) => {
            return `<span class="math-inline">$${formula.trim()}$</span>`;
        });

        return text;
    }

    // 渲染段落
    renderMarkdownParagraphs(text) {
        // 分割成段落（双换行符分隔）
        const paragraphs = text.split(/\n\s*\n/);
        let result = '';

        paragraphs.forEach(paragraph => {
            const trimmed = paragraph.trim();
            if (!trimmed) return;

            // 检查是否是块级元素（不需要包装在 p 标签中）
            const blockElements = /^<(h[1-6]|table|ul|ol|blockquote|pre|hr|div)/;
            if (blockElements.test(trimmed)) {
                result += trimmed + '\n\n';
            } else {
                // 普通段落，包装在 p 标签中
                result += `<p>${trimmed.replace(/\n/g, '<br>')}</p>\n\n`;
            }
        });

        return result.trim();
    }

    // 绑定原始结果编辑事件
    bindRawResultEditEvent(rawTextElement, renderedContainer, mode) {
        // 移除之前的事件监听器
        if (rawTextElement._inputHandler) {
            rawTextElement.removeEventListener('input', rawTextElement._inputHandler);
        }

        // 创建新的事件处理器
        rawTextElement._inputHandler = async () => {
            const content = rawTextElement.value;

            // 渲染结果
            await this.renderResult(content, mode, renderedContainer);

            // 如果有内容且操作按钮未显示，则显示操作按钮
            const actionButtonsContainer = document.getElementById('dual-action-buttons');
            if (content.trim() && actionButtonsContainer && actionButtonsContainer.style.display === 'none') {
                this.createDualActionButtons(mode);
            }
            // 如果没有内容，隐藏操作按钮
            else if (!content.trim() && actionButtonsContainer) {
                actionButtonsContainer.style.display = 'none';
            }
        };

        // 绑定新的事件监听器
        rawTextElement.addEventListener('input', rawTextElement._inputHandler);
    }

    // HTML转义函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 等待KaTeX加载
    async waitForKaTeX(maxWait = 5000) {
        const startTime = Date.now();
        let attempts = 0;
        const maxAttempts = 50;

        while (typeof katex === 'undefined' && (Date.now() - startTime) < maxWait && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            // 每隔1秒输出一次调试信息
            if (attempts % 10 === 0) {
                // 等待KaTeX加载中...
            }
        }

        const isLoaded = typeof katex !== 'undefined';
        return isLoaded;
    }

    // 渲染公式
    async renderFormula(text) {
        if (!text || !text.trim()) {
            return '';
        }

        try {
            // 使用KaTeX加载器
            if (window.katexLoader) {
                const status = window.katexLoader.getStatus();

                // 如果处于备用模式，直接返回备用渲染
                if (status.fallbackMode) {
                    return window.katexLoader.renderFallback(text);
                }
            }

            // 等待KaTeX加载
            const katexLoaded = window.katexLoader ?
                await window.katexLoader.waitForKaTeX() :
                await this.waitForKaTeX();

            if (!katexLoaded) {
                // 如果KaTeX不可用，显示错误信息和原始文本
                return `<div class="formula-block-simple">
                    <div class="formula-error">
                        ⚠️ 数学渲染引擎加载失败
                        <div style="font-size: 12px; margin-top: 5px; font-weight: normal;">
                            可能原因：网络连接问题或插件环境限制
                        </div>
                    </div>
                    <div class="formula-text">
                        ${this.escapeHtml(text)}
                    </div>
                </div>`;
            }

            let rendered = '';
            const lines = text.split('\n');

            for (let line of lines) {
                line = line.trim();
                if (!line) {
                    rendered += '<br>';
                    continue;
                }

                try {
                    // 检查是否包含LaTeX公式标记
                    if (line.includes('$$') || line.includes('$') ||
                        line.includes('\\') || line.includes('{') || line.includes('}')) {

                        // 处理块级公式 $$...$$
                        if (line.startsWith('$$') && line.endsWith('$$')) {
                            const formula = line.slice(2, -2).trim();
                            if (window.katexLoader) {
                                const katexHtml = await window.katexLoader.renderFormula(formula, { displayMode: true });
                                rendered += `<div class="formula-display">${katexHtml}</div>`;
                            } else if (typeof katex !== 'undefined') {
                                const katexHtml = katex.renderToString(formula, {
                                    displayMode: true,
                                    throwOnError: false,
                                    errorColor: '#cc0000',
                                    strict: false
                                });
                                rendered += `<div class="formula-display">${katexHtml}</div>`;
                            } else {
                                rendered += `<div class="formula-text">${line}</div>`;
                            }
                        }
                        // 处理行内公式 $...$
                        else if (line.startsWith('$') && line.endsWith('$') && line.length > 2) {
                            const formula = line.slice(1, -1).trim();
                            if (window.katexLoader) {
                                const katexHtml = await window.katexLoader.renderFormula(formula, { displayMode: false });
                                rendered += `<span class="formula-inline">${katexHtml}</span><br>`;
                            } else if (typeof katex !== 'undefined') {
                                const katexHtml = katex.renderToString(formula, {
                                    displayMode: false,
                                    throwOnError: false,
                                    errorColor: '#cc0000',
                                    strict: false
                                });
                                rendered += `<span class="formula-inline">${katexHtml}</span><br>`;
                            } else {
                                rendered += `<div class="formula-text">${line}</div>`;
                            }
                        }
                        // 处理包含LaTeX语法的文本
                        else {
                            // 尝试作为块级公式渲染
                            if (window.katexLoader) {
                                const katexHtml = await window.katexLoader.renderFormula(line, { displayMode: true });
                                rendered += `<div class="formula-display">${katexHtml}</div>`;
                            } else if (typeof katex !== 'undefined') {
                                const katexHtml = katex.renderToString(line, {
                                    displayMode: true,
                                    throwOnError: false,
                                    errorColor: '#cc0000',
                                    strict: false
                                });
                                rendered += `<div class="formula-display">${katexHtml}</div>`;
                            } else {
                                rendered += `<div class="formula-text">${line}</div>`;
                            }
                        }
                    } else {
                        // 普通文本，尝试作为公式渲染
                        if (window.katexLoader) {
                            const katexHtml = await window.katexLoader.renderFormula(line, { displayMode: true });
                            rendered += `<div class="formula-display">${katexHtml}</div>`;
                        } else if (typeof katex !== 'undefined') {
                            const katexHtml = katex.renderToString(line, {
                                displayMode: true,
                                throwOnError: false,
                                errorColor: '#cc0000',
                                strict: false
                            });
                            rendered += `<div class="formula-display">${katexHtml}</div>`;
                        } else {
                            rendered += `<div class="formula-text">${line}</div>`;
                        }
                    }
                } catch (error) {
                    // 如果单行渲染失败，显示原始文本
                    rendered += `<div class="formula-text">${line}</div>`;
                }
            }

            if (rendered) {
                return rendered;
            } else if (window.katexLoader) {
                // 使用KaTeX加载器渲染整个文本
                return await window.katexLoader.renderFormula(text, { displayMode: true });
            } else if (typeof katex !== 'undefined') {
                try {
                    return `<div class="formula-display">${katex.renderToString(text, {
                        displayMode: true,
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false
                    })}</div>`;
                } catch (katexError) {
                    console.warn('KaTeX渲染失败:', katexError);
                    return `<div class="formula-text">${text}</div>`;
                }
            } else {
                return `<div class="formula-text">${text}</div>`;
            }

        } catch (error) {
            return `<div class="render-error">
                <div class="render-error-title">公式渲染失败</div>
                <div>${error.message}</div>
                <div class="formula-fallback">${text}</div>
            </div>`;
        }
    }

    // 渲染表格
    renderTable(text) {
        if (!text || !text.trim()) {
            return '';
        }

        try {
            // 尝试解析Markdown格式的表格
            const lines = text.trim().split('\n');
            if (lines.length < 2) {
                return `<div class="render-error">
                    <div class="render-error-title">表格格式错误</div>
                    <div>至少需要表头和分隔行</div>
                </div>`;
            }

            let tableHtml = '<table>';
            let isFirstRow = true;
            let hasHeaderSeparator = false;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                // 检查是否是分隔行
                if (line.match(/^[\|\s\-:]+$/)) {
                    hasHeaderSeparator = true;
                    continue;
                }

                // 解析表格行
                const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);

                if (cells.length === 0) continue;

                const tag = (isFirstRow && !hasHeaderSeparator) || (i === 0) ? 'th' : 'td';
                tableHtml += '<tr>';
                cells.forEach(cell => {
                    tableHtml += `<${tag}>${cell}</${tag}>`;
                });
                tableHtml += '</tr>';

                if (isFirstRow && hasHeaderSeparator) {
                    isFirstRow = false;
                }
            }

            tableHtml += '</table>';
            return tableHtml;
        } catch (error) {
            return `<div class="render-error">
                <div class="render-error-title">表格渲染失败</div>
                <div>${error.message}</div>
            </div>`;
        }
    }

    // 渲染Markdown
    renderMarkdown(text) {
        if (!text || !text.trim()) {
            return '';
        }

        try {
            let rendered = text;

            // 转义HTML特殊字符（但保留已有的HTML标签）
            rendered = rendered.replace(/&(?!amp;|lt;|gt;|quot;|#)/g, '&amp;')
                              .replace(/</g, '&lt;')
                              .replace(/>/g, '&gt;');

            // 处理表格（必须在其他处理之前）
            rendered = this.renderMarkdownTables(rendered);

            // 水平分割线
            rendered = rendered.replace(/^(---+|\*\*\*+)$/gm, '<hr>');

            // 代码块（必须在其他处理之前，避免内容被误处理）
            rendered = rendered.replace(/```(\w*)\n?([\s\S]*?)```/g, (match, lang, code) => {
                const language = lang ? ` class="language-${lang}"` : '';
                return `<pre><code${language}>${code.trim()}</code></pre>`;
            });

            // 行内代码
            rendered = rendered.replace(/`([^`\n]+)`/g, '<code>$1</code>');

            // 标题（从最长的开始处理）
            rendered = rendered.replace(/^###### (.*$)/gm, '<h6>$1</h6>');
            rendered = rendered.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
            rendered = rendered.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
            rendered = rendered.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            rendered = rendered.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            rendered = rendered.replace(/^# (.*$)/gm, '<h1>$1</h1>');

            // 图片（必须在链接之前处理）
            rendered = rendered.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;">');

            // 链接
            rendered = rendered.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

            // 粗体和斜体（支持 ** 和 __ 语法）
            rendered = rendered.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
            rendered = rendered.replace(/__([^_]+)__/g, '<strong>$1</strong>');
            rendered = rendered.replace(/\*([^*]+)\*/g, '<em>$1</em>');
            rendered = rendered.replace(/_([^_]+)_/g, '<em>$1</em>');

            // 删除线
            rendered = rendered.replace(/~~([^~]+)~~/g, '<del>$1</del>');

            // 数学公式（LaTeX语法）
            rendered = this.renderMathFormulas(rendered);

            // 处理列表
            rendered = this.renderMarkdownLists(rendered);

            // 引用块
            rendered = rendered.replace(/^> (.+$)/gm, '<blockquote>$1</blockquote>');
            // 处理多行引用
            rendered = rendered.replace(/(<blockquote>.*?<\/blockquote>(\n<blockquote>.*?<\/blockquote>)*)/gs, function(match) {
                const content = match.replace(/<\/?blockquote>/g, '').replace(/\n/g, '<br>');
                return `<blockquote>${content}</blockquote>`;
            });

            // 段落处理
            rendered = this.renderMarkdownParagraphs(rendered);

            return rendered;
        } catch (error) {
            console.error('Markdown渲染错误:', error);
            return `<div class="render-error">
                <div class="render-error-title">Markdown渲染失败</div>
                <div>${error.message}</div>
            </div>`;
        }
    }

    // 显示置信度
    showConfidence(confidence) {
        let confidenceEl = document.getElementById('confidence-info');
        if (!confidenceEl) {
            const resultHeader = document.querySelector('.result-header');
            if (!resultHeader) {
                console.warn('未找到result-header元素，无法显示置信度信息');
                return;
            }
            confidenceEl = document.createElement('div');
            confidenceEl.id = 'confidence-info';
            confidenceEl.className = 'confidence-info';
            resultHeader.appendChild(confidenceEl);
        }
        
        const percentage = Math.round(confidence * 100);
        confidenceEl.textContent = `置信度: ${percentage}%`;
        confidenceEl.className = `confidence-info ${percentage >= 80 ? 'high' : percentage >= 60 ? 'medium' : 'low'}`;
    }

    // 清空结果
    clearResult() {
        // 清空单栏模式的结果
        const resultText = document.getElementById('result-text');
        if (resultText) {
            resultText.value = '';
        }

        // 清空双栏模式的结果
        const rawResultText = document.getElementById('raw-result-text');
        if (rawResultText) {
            rawResultText.value = '';
        }

        const renderedResult = document.getElementById('rendered-result');
        if (renderedResult) {
            renderedResult.innerHTML = '';
        }

        // 清空操作按钮
        const actionButtonsContainer = document.getElementById('dual-action-buttons');
        if (actionButtonsContainer) {
            actionButtonsContainer.innerHTML = '';
            actionButtonsContainer.style.display = 'none';
        }

        // 清空原始文本
        this.originalResultText = '';

        // 恢复默认占位符
        this.restoreDefaultPlaceholder();

        const confidenceEl = document.getElementById('confidence-info');
        if (confidenceEl) {
            confidenceEl.remove();
        }

        // 根据当前识别模式重新设置布局状态
        const ocrPlugin = this.getOcrPlugin();
        if (ocrPlugin && ocrPlugin.currentRecognitionMode) {
            const currentMode = ocrPlugin.currentRecognitionMode;
            if (currentMode !== 'text') {
                // 对于非文字模式，重新显示空白状态的双栏布局
                this.showDualColumnLayout(currentMode, true);
            }
        }

        // 注意：重新识别按钮是静态存在的，不应该被移除
        // 它始终存在于导航栏中，无需动态添加或删除

        // 清空图片预览
        this.clearImagePreview();

        // 清空保存的图片
        if (window.ocrPlugin) {
            window.ocrPlugin.lastImageBase64 = null;
        }

        // 恢复到服务的真实连接状态，而不是强制设置为就绪
        this.restoreServiceStatus();
    }

    // 显示通知
    showNotification(message, type = 'success', duration = 3000) {
        const toastContainer = this.getOrCreateToastContainer();
        const toast = this.createToast(message, type);

        // 将新Toast插入到容器的开头，让新消息显示在上方
        toastContainer.insertBefore(toast, toastContainer.firstChild);

        // 等待下一个渲染周期后更新位置和显示Toast
        requestAnimationFrame(() => {
            this.updateToastPositions();
            this.showToast(toast, duration);
        });

        return toast;
    }

    // 获取或创建Toast容器
    getOrCreateToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        return container;
    }

    // 创建Toast元素
    createToast(message, type) {
        const processedMessage = this.processToastMessage(message);
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = processedMessage.text;

        if (processedMessage.needsWrap) {
            Object.assign(toast.style, {
                whiteSpace: 'normal',
                maxWidth: 'min(350px, calc(100vw - 2rem))',
                lineHeight: '1.4'
            });
        }

        // 初始化Toast的位置状态
        toast._isPositioned = false;

        return toast;
    }

    // 更新所有Toast的位置
    updateToastPositions() {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toasts = Array.from(container.children);
        let currentTop = 0;

        // 正向遍历，因为新Toast已经被插入到开头，所以第一个就是最新的
        toasts.forEach((toast, index) => {
            // 使用CSS自定义属性设置垂直偏移
            toast.style.setProperty('--toast-offset', `${currentTop}px`);
            toast._isPositioned = true;

            // 强制重新计算布局以获取准确高度
            toast.offsetHeight;

            // 计算下一个Toast的位置（当前Toast高度 + 间距）
            const toastHeight = toast.getBoundingClientRect().height || 60; // 使用getBoundingClientRect获取更准确的高度
            currentTop += toastHeight + 8; // 8px间距
        });
    }

    // 显示Toast
    showToast(toast, duration) {
        // 安全检查
        if (!toast || !toast.classList) {
            console.error('showToast: 无效的toast元素', toast);
            return;
        }

        // 确保Toast已经被定位
        if (!toast._isPositioned) {
            this.updateToastPositions();
        }

        // 触发显示动画
        requestAnimationFrame(() => {
            if (toast && toast.classList) {
                toast.classList.add('show');
            }
        });

        // 设置自动隐藏
        const hideTimeout = setTimeout(() => this.hideToast(toast), duration);
        toast._hideTimeout = hideTimeout;
        this.notifications.push(toast);

        // 点击隐藏
        toast.addEventListener('click', () => {
            clearTimeout(toast._hideTimeout);
            this.hideToast(toast);
        });
    }

    // 处理Toast消息文本
    processToastMessage(message) {
        const maxLineLength = 40; // 增加每行最大字符数，适应中文
        const maxLines = 4; // 增加最大行数
        const maxTotalLength = maxLineLength * maxLines; // 总最大字符数

        // 如果消息很短，直接返回
        if (message.length <= maxLineLength) {
            return { text: message, needsWrap: false };
        }

        // 如果消息过长，截断并添加省略号
        if (message.length > maxTotalLength) {
            return {
                text: message.substring(0, maxTotalLength - 3) + '...',
                needsWrap: true
            };
        }

        // 中等长度的消息，智能换行
        // 对于中文，优先按标点符号分割，其次按空格分割
        let words;
        if (/[\u4e00-\u9fa5]/.test(message)) {
            // 包含中文，按标点符号和空格分割
            words = message.split(/([，。！？；：、\s]+)/).filter(word => word.trim());
        } else {
            // 纯英文，按空格分割
            words = message.split(' ');
        }

        const lines = [];
        let currentLine = '';

        for (const word of words) {
            const testLine = currentLine ? currentLine + word : word;

            if (testLine.length <= maxLineLength) {
                currentLine = testLine;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    // 单个词太长，强制截断
                    if (word.length > maxLineLength) {
                        lines.push(word.substring(0, maxLineLength));
                        currentLine = word.substring(maxLineLength);
                    } else {
                        currentLine = word;
                    }
                }

                if (lines.length >= maxLines - 1) {
                    break;
                }
            }
        }

        if (currentLine && lines.length < maxLines) {
            lines.push(currentLine);
        }

        return {
            text: lines.join('\n'),
            needsWrap: true
        };
    }

    // 隐藏Toast
    hideToast(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.remove('show');
        toast.classList.add('hide');

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }

            // 从通知数组中移除
            const index = this.notifications.indexOf(toast);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }

            // 更新剩余Toast的位置
            this.updateToastPositions();

            // 如果容器为空，移除容器
            const container = document.getElementById('toast-container');
            if (container && container.children.length === 0) {
                container.remove();
            }
        }, 200);
    }

    // 显示错误
    showError(message) {
        // 确保隐藏加载状态
        this.hideLoading();

        // 恢复默认占位符
        this.restoreDefaultPlaceholder();

        // 显示错误通知
        this.showNotification(message, 'error', 5000);

        // 更新状态显示为错误
        this.updateRecognitionStatus('error', '识别失败');
    }

    // 显示成功消息
    showSuccess(message, duration = 3000) {
        return this.showNotification(message, 'success', duration);
    }

    // 显示警告消息
    showWarning(message, duration = 4000) {
        return this.showNotification(message, 'warning', duration);
    }

    // 显示信息消息
    showInfo(message, duration = 3000) {
        return this.showNotification(message, 'info', duration);
    }

    // 显示状态检测结果
    showStatusResult(serviceName, status, message) {
        if (status === 'success') {
            this.showSuccess(`${serviceName} 服务连接正常`);
        } else if (status === 'error') {
            this.showError(`${serviceName} 服务连接失败: ${message}`);
        } else {
            this.showWarning(`${serviceName} 服务状态未知`);
        }
    }

    // 显示换行符切换操作结果
    showLinebreakRemoved(removeLinebreaks) {
        if (removeLinebreaks) {
            this.showInfo('去除换行符');
        } else {
            this.showInfo('保留换行符');
        }
    }

    // 显示识别成功消息
    showRecognitionSuccess(hasConfidence = false, confidence = null) {
        let message = '文字识别完成';
        if (hasConfidence && confidence !== null) {
            message += ` (置信度: ${Math.round(confidence * 100)}%)`;
        }
        this.showSuccess(message);
    }

    // 显示服务平台切换成功
    showServiceSwitched(serviceName, platformName = null) {
        let message = `已切换到 ${this.getServiceDisplayName(serviceName)}`;
        if (platformName) {
            message += ` (${this.getPlatformDisplayName(platformName)})`;
        }
        this.showSuccess(message);
    }

    // 显示配置未保存提示
    showConfigUnsaved() {
        this.showWarning('配置已修改但未保存，当前使用的是之前保存的配置');
    }

    // 获取服务显示名称
    getServiceDisplayName(serviceName) {
        const serviceNames = {
            'baidu': '百度智能云',
            'tencent': '腾讯云',
            'aliyun': '阿里云',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Gemini',
            'alibaba': '阿里云百炼',
            'bytedance': '火山引擎',
            'ocrpro': 'OCR Pro',
            'utools': 'uTools AI',
            'llm': 'AI视觉模型'
        };
        return serviceNames[serviceName] || serviceName;
    }

    // 获取平台显示名称
    getPlatformDisplayName(platformName) {
        const platformNames = {
            'openai': 'OpenAI',
            'anthropic': 'Claude',
            'google': 'Gemini',
            'alibaba': 'Qwen',
            'bytedance': '豆包 AI',
            'utools': 'uTools AI'
        };
        return platformNames[platformName] || platformName;
    }

    // 获取模型能力类型
    getModelCapabilities(service, modelId, modelData = null) {
        if (!window.modelCapabilityDetector) {
            return [];
        }

        // 统一使用能力检测器的逻辑，让重构后的uTools处理生效
        return window.modelCapabilityDetector.detectCapabilities(service, modelId, modelData);
    }

    // 构建用于能力检测的模型数据对象
    buildModelDataForCapabilityDetection(service, modelId, displayName) {
        const modelData = {
            name: displayName,
            displayName: displayName,
            platform: service
        };

        // 对于uTools平台，添加额外的信息用于真实服务商检测
        if (service === 'utools') {
            modelData.label = displayName;

            // 对于uTools平台，尝试从配置中获取更多信息
            try {
                const platformConfig = window.ocrPlugin?.config?.[service];
                if (platformConfig?.modelNameMap) {
                    const originalName = Object.keys(platformConfig.modelNameMap).find(
                        key => platformConfig.modelNameMap[key] === displayName
                    );
                    if (originalName) {
                        modelData.originalId = originalName;
                    } else {
                        modelData.originalId = modelId;
                    }
                } else {
                    modelData.originalId = modelId;
                }
            } catch (error) {
                console.warn('获取uTools模型额外信息失败:', error);
                modelData.originalId = modelId;
            }
        }

        return modelData;
    }

    // 判断是否为AI模型
    isAIModel(service, modelId) {
        // 传统OCR服务不是AI模型
        const traditionalServices = ['baidu', 'tencent', 'aliyun'];
        return !traditionalServices.includes(service);
    }

    // 判断是否为视觉模型
    isVisionModel(service, modelId, modelData = null) {
        if (!window.modelCapabilityDetector) {
            return false;
        }

        return window.modelCapabilityDetector.isVisionModel(service, modelId, modelData);
    }

    // 判断是否为推理模型
    isReasoningModel(service, modelId, modelData = null) {
        if (!window.modelCapabilityDetector) {
            return false;
        }

        return window.modelCapabilityDetector.isReasoningModel(service, modelId, modelData);
    }

    // 生成功能图标HTML
    generateCapabilityIcons(capabilities) {
        if (!capabilities || capabilities.length === 0) {
            return '';
        }

        const iconMap = {
            text: {
                svg: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="capability-icon text-icon">
                    <path d="M15 12h6"/>
                    <path d="M15 6h6"/>
                    <path d="m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13"/>
                    <path d="M3 18h18"/>
                    <path d="M3.92 11h6.16"/>
                </svg>`,
                color: '#3b82f6'  // 蓝色
            },
            vision: {
                svg: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="capability-icon vision-icon">
                    <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>`,
                color: '#10b981'  // 绿色
            },
            reasoning: {
                svg: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="capability-icon reasoning-icon">
                    <path d="M12 18V5"/>
                    <path d="M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4"/>
                    <path d="M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5"/>
                    <path d="M17.997 5.125a4 4 0 0 1 2.526 5.77"/>
                    <path d="M18 18a4 4 0 0 0 2-7.464"/>
                    <path d="M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517"/>
                    <path d="M6 18a4 4 0 0 1-2-7.464"/>
                    <path d="M6.003 5.125a4 4 0 0 0-2.526 5.77"/>
                </svg>`,
                color: '#8b5cf6'  // 紫色
            }
        };

        // 按照文本、视觉、推理的顺序排列
        const orderedCapabilities = ['text', 'vision', 'reasoning'].filter(cap => capabilities.includes(cap));

        const iconsHtml = orderedCapabilities.map(capability => {
            const icon = iconMap[capability];
            if (!icon) return '';

            return `<span class="capability-icon-wrapper" data-capability="${capability}" style="color: ${icon.color};" title="${this.getCapabilityTitle(capability)}">${icon.svg}</span>`;
        }).join('');

        return iconsHtml ? `<div class="capability-icons">${iconsHtml}</div>` : '';
    }

    // 获取能力标题
    getCapabilityTitle(capability) {
        const titles = {
            text: '文本处理',
            vision: '视觉识别',
            reasoning: '推理思考'
        };
        return titles[capability] || capability;
    }

    // 获取服务商图标SVG
    getServiceIcon(serviceName) {
        // 生成唯一ID以避免SVG渐变冲突
        const uniqueId = `icon-${serviceName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const icons = {
            'baidu': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>BaiduCloud</title><path d="M21.715 5.61l-3.983 2.31a.903.903 0 01-.896 0L12.44 5.384a.903.903 0 00-.897 0L7.156 7.92a.903.903 0 01-.896 0L2.276 5.617 12.002 0l9.713 5.61z" fill="#5BCA87"></path><path d="M18.641 9.467a.89.89 0 00-.438.77v5.072a.896.896 0 01-.445.77l-4.428 2.51a.884.884 0 00-.445.777v4.607l4.429-2.536 5.31-3.047V7.157l-3.983 2.31z" fill="#EC5D3E"></path><path d="M10.98 18.941a.936.936 0 00-.305-.352l-4.429-2.516a.903.903 0 01-.431-.764v-5.078a.89.89 0 00-.452-.757l-.451-.26L1.38 7.158V18.39l5.311 3.047L11.126 24v-4.608a.881.881 0 00-.146-.45z" fill="#2464F5"></path></svg>`,

            'tencent': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>TencentCloud</title><path d="M20.0483 17.1416C19.6945 17.4914 18.987 18.0161 17.7488 18.0161C17.2182 18.0161 16.5991 18.0161 16.3338 18.0161C15.98 18.0161 13.3268 18.0161 10.143 18.0161C12.4424 15.8298 14.3881 13.9932 14.565 13.8183C14.7419 13.6434 15.1841 13.2061 15.6263 12.8563C16.5107 12.0692 17.2182 11.9817 17.8373 11.9817C18.7217 11.9817 19.4292 12.3316 20.0483 12.8563C21.2864 13.9932 21.2864 16.0047 20.0483 17.1416ZM21.5518 11.457C20.6674 10.495 19.3408 9.88281 17.9257 9.88281C16.6875 9.88281 15.6263 10.3201 14.6534 11.0197C14.2997 11.3695 13.769 11.7194 13.3268 12.2441C12.9731 12.5939 5.36719 19.9401 5.36719 19.9401C5.80939 20.0276 6.34003 20.0276 6.78223 20.0276C7.22443 20.0276 16.0685 20.0276 16.4222 20.0276C17.1298 20.0276 17.6604 20.0276 18.191 19.9401C19.3408 19.8527 20.4905 19.4154 21.4633 18.5409C23.4975 16.6168 23.4975 13.381 21.5518 11.457Z" fill="#00A3FF"></path><path d="M9.1701 10.9323C8.19726 10.2326 7.22442 9.88281 6.07469 9.88281C4.65965 9.88281 3.33304 10.495 2.44864 11.457C0.502952 13.4685 0.502952 16.6168 2.53708 18.6283C3.42148 19.4154 4.30589 19.8527 5.36717 19.9401L7.4013 18.0161C7.04754 18.0161 6.60533 18.0161 6.25157 18.0161C5.10185 17.9287 4.39433 17.5789 3.95212 17.1416C2.71396 15.9172 2.71396 13.9932 3.86368 12.7688C4.48277 12.1566 5.19029 11.8943 6.07469 11.8943C6.60533 11.8943 7.4013 11.9817 8.19726 12.7688C8.55102 13.1186 9.52386 13.8183 9.87763 14.1681H9.96607L11.2927 12.8563V12.7688C10.6736 12.1566 9.70075 11.3695 9.1701 10.9323Z" fill="#00C8DC"></path><path d="M18.4564 8.74536C17.4836 6.12171 14.9188 4.28516 12.0003 4.28516C8.5511 4.28516 5.80945 6.82135 5.27881 9.96973C5.54413 9.96973 5.80945 9.88228 6.16321 9.88228C6.51697 9.88228 6.95917 9.96973 7.31294 9.96973C7.75514 7.78336 9.70082 6.20917 12.0003 6.20917C13.946 6.20917 15.6263 7.34608 16.4223 9.00773C16.4223 9.00773 16.5107 9.09518 16.5107 9.00773C17.1298 8.92027 17.8373 8.74536 18.4564 8.74536C18.4564 8.83282 18.4564 8.83282 18.4564 8.74536Z" fill="#006EFF"></path></svg>`,

            'aliyun': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>AlibabaCloud</title><path d="M14.752 4.64h5.274C22.242 4.64 24 6.475 24 8.691V15.8a3.947 3.947 0 01-3.974 3.975h-5.274l1.299-1.835 3.822-1.222c.688-.23 1.146-.918 1.146-1.605v-5.81c0-.687-.458-1.375-1.146-1.605L16.05 6.475l-1.3-1.835zM2.98 15.111c0 .688.46 1.376 1.147 1.606l3.822 1.146 1.3 1.835H3.974A3.947 3.947 0 010 15.723V8.69c0-2.216 1.758-4.05 3.975-4.05h5.273L7.95 6.474 4.127 7.697c-.688.23-1.146.918-1.146 1.606v5.808z" fill="#FF6A00"></path><path d="M16.051 11.213H8.025v1.835h8.026v-1.835z" fill="#FF6A00"></path></svg>`,

            'openai': `<svg fill="currentColor" fill-rule="evenodd" height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>OpenAI</title><path d="M21.55 10.004a5.416 5.416 0 00-.478-4.501c-1.217-2.09-3.662-3.166-6.05-2.66A5.59 5.59 0 0010.831 1C8.39.995 6.224 2.546 5.473 4.838A5.553 5.553 0 001.76 7.496a5.487 5.487 0 00.691 6.5 5.416 5.416 0 00.477 4.502c1.217 2.09 3.662 3.165 6.05 2.66A5.586 5.586 0 0013.168 23c2.443.006 4.61-1.546 5.361-3.84a5.553 5.553 0 003.715-2.66 5.488 5.488 0 00-.693-6.497v.001zm-8.381 11.558a4.199 4.199 0 01-2.675-.954c.034-.018.093-.05.132-.074l4.44-2.53a.71.71 0 00.364-.623v-6.176l1.877 1.069c.02.01.033.029.036.05v5.115c-.003 2.274-1.87 4.118-4.174 4.123zM4.192 17.78a4.059 4.059 0 01-.498-2.763c.032.02.09.055.131.078l4.44 2.53c.225.13.504.13.73 0l5.42-3.088v2.138a.068.068 0 01-.027.057L9.9 19.288c-1.999 1.136-4.552.46-5.707-1.51h-.001zM3.023 8.216A4.15 4.15 0 015.198 6.41l-.002.151v5.06a.711.711 0 00.364.624l5.42 3.087-1.876 1.07a.067.067 0 01-.063.005l-4.489-2.559c-1.995-1.14-2.679-3.658-1.53-5.63h.001zm15.417 3.54l-5.42-3.088L14.896 7.6a.067.067 0 01.063-.006l4.489 2.557c1.998 1.14 2.683 3.662 1.529 5.633a4.163 4.163 0 01-2.174 1.807V12.38a.71.71 0 00-.363-.623zm1.867-2.773a6.04 6.04 0 00-.132-.078l-4.44-2.53a.731.731 0 00-.729 0l-5.42 3.088V7.325a.068.068 0 01.027-.057L14.1 4.713c2-1.137 4.555-.46 5.707 1.513.487.833.664 1.809.499 2.757h.001zm-11.741 3.81l-1.877-1.068a.065.065 0 01-.036-.051V6.559c.001-2.277 1.873-4.122 4.181-4.12.976 0 1.92.338 2.671.954-.034.018-.092.05-.131.073l-4.44 2.53a.71.71 0 00-.365.623l-.003 6.173v.002zm1.02-2.168L12 9.25l2.414 1.375v2.75L12 14.75l-2.415-1.375v-2.75z"></path></svg>`,

            'anthropic': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Claude</title><path d="M4.709 15.955l4.72-2.647.08-.23-.08-.128H9.2l-.79-.048-2.698-.073-2.339-.097-2.266-.122-.571-.121L0 11.784l.055-.352.48-.321.686.06 1.52.103 2.278.158 1.652.097 2.449.255h.389l.055-.157-.134-.098-.103-.097-2.358-1.596-2.552-1.688-1.336-.972-.724-.491-.364-.462-.158-1.008.656-.722.881.06.225.061.893.686 1.908 1.476 2.491 1.833.365.304.145-.103.019-.073-.164-.274-1.355-2.446-1.446-2.49-.644-1.032-.17-.619a2.97 2.97 0 01-.104-.729L6.283.134 6.696 0l.996.134.42.364.62 1.414 1.002 2.229 1.555 3.03.456.898.243.832.091.255h.158V9.01l.128-1.706.237-2.095.23-2.695.08-.76.376-.91.747-.492.584.28.48.685-.067.444-.286 1.851-.559 2.903-.364 1.942h.212l.243-.242.985-1.306 1.652-2.064.73-.82.85-.904.547-.431h1.033l.76 1.129-.34 1.166-1.064 1.347-.881 1.142-1.264 1.7-.79 1.36.073.11.188-.02 2.856-.606 1.543-.28 1.841-.315.833.388.091.395-.328.807-1.969.486-2.309.462-3.439.813-.042.03.049.061 1.549.146.662.036h1.622l3.02.225.79.522.474.638-.079.485-1.215.62-1.64-.389-3.829-.91-1.312-.329h-.182v.11l1.093 1.068 2.006 1.81 2.509 2.33.127.578-.322.455-.34-.049-2.205-1.657-.851-.747-1.926-1.62h-.128v.17l.444.649 2.345 3.521.122 1.08-.17.353-.608.213-.668-.122-1.374-1.925-1.415-2.167-1.143-1.943-.14.08-.674 7.254-.316.37-.729.28-.607-.461-.322-.747.322-1.476.389-1.924.315-1.53.286-1.9.17-.632-.012-.042-.14.018-1.434 1.967-2.18 2.945-1.726 1.845-.414.164-.717-.37.067-.662.401-.589 2.388-3.036 1.44-1.882.93-1.086-.006-.158h-.055L4.132 18.56l-1.13.146-.487-.456.061-.746.231-.243 1.908-1.312-.006.006z" fill="#D97757" fill-rule="nonzero"></path></svg>`,

            'google': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Gemini</title><defs><linearGradient id="${uniqueId}-google" x1="0%" x2="68.73%" y1="100%" y2="30.395%"><stop offset="0%" stop-color="#1C7DFF"></stop><stop offset="52.021%" stop-color="#1C69FF"></stop><stop offset="100%" stop-color="#F0DCD6"></stop></linearGradient></defs><path d="M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12" fill="url(#${uniqueId}-google)" fill-rule="nonzero"></path></svg>`,

            'alibaba': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Qwen</title><defs><linearGradient id="${uniqueId}-alibaba" x1="0%" x2="100%" y1="0%" y2="0%"><stop offset="0%" stop-color="#00055F" stop-opacity=".84"></stop><stop offset="100%" stop-color="#6F69F7" stop-opacity=".84"></stop></linearGradient></defs><path d="M12.604 1.34c.393.69.784 1.382 1.174 2.075a.18.18 0 00.157.091h5.552c.174 0 .322.11.446.327l1.454 2.57c.19.337.24.478.024.837-.26.43-.513.864-.76 1.3l-.367.658c-.106.196-.223.28-.04.512l2.652 4.637c.172.301.111.494-.043.77-.437.785-.882 1.564-1.335 2.34-.159.272-.352.375-.68.37-.777-.016-1.552-.01-2.327.016a.099.099 0 00-.081.05 575.097 575.097 0 01-2.705 4.74c-.169.293-.38.363-.725.364-.997.003-2.002.004-3.017.002a.537.537 0 01-.465-.271l-1.335-2.323a.09.09 0 00-.083-.049H4.982c-.285.03-.553-.001-.805-.092l-1.603-2.77a.543.543 0 01-.002-.54l1.207-2.12a.198.198 0 000-.197 550.951 550.951 0 01-1.875-3.272l-.79-1.395c-.16-.31-.173-.496.095-.965.465-.813.927-1.625 1.387-2.436.132-.234.304-.334.584-.335a338.3 338.3 0 012.589-.001.124.124 0 00.107-.063l2.806-4.895a.488.488 0 01.422-.246c.524-.001 1.053 0 1.583-.006L11.704 1c.341-.003.724.032.9.34zm-3.432.403a.06.06 0 00-.052.03L6.254 6.788a.157.157 0 01-.135.078H3.253c-.056 0-.07.025-.041.074l5.81 10.156c.025.042.013.062-.034.063l-2.795.015a.218.218 0 00-.2.116l-1.32 2.31c-.044.078-.021.118.068.118l5.716.008c.046 0 .08.02.104.061l1.403 2.454c.046.081.092.082.139 0l5.006-8.76.783-1.382a.055.055 0 01.096 0l1.424 2.53a.122.122 0 00.107.062l2.763-.02a.04.04 0 00.035-.02.041.041 0 000-.04l-2.9-5.086a.108.108 0 010-.113l.293-.507 1.12-1.977c.024-.041.012-.062-.035-.062H9.2c-.059 0-.073-.026-.043-.077l1.434-2.505a.107.107 0 000-.114L9.225 1.774a.06.06 0 00-.053-.031zm6.29 8.02c.046 0 .058.02.034.06l-.832 1.465-2.613 4.585a.056.056 0 01-.05.029.058.058 0 01-.05-.029L8.498 9.841c-.02-.034-.01-.052.028-.054l.216-.012 6.722-.012z" fill="url(#${uniqueId}-alibaba)" fill-rule="nonzero"></path></svg>`,

            'bytedance': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Doubao</title><path d="M5.31 15.756c.172-3.75 1.883-5.999 2.549-6.739-3.26 2.058-5.425 5.658-6.358 8.308v1.12C1.501 21.513 4.226 24 7.59 24a6.59 6.59 0 002.2-.375c.353-.12.7-.248 1.039-.378.913-.899 1.65-1.91 2.243-2.992-4.877 2.431-7.974.072-7.763-4.5l.002.001z" fill="#1E37FC"></path><path d="M22.57 10.283c-1.212-.901-4.109-2.404-7.397-2.8.295 3.792.093 8.766-2.1 12.773a12.782 12.782 0 01-2.244 2.992c3.764-1.448 6.746-3.457 8.596-5.219 2.82-2.683 3.353-5.178 3.361-6.66a2.737 2.737 0 00-.216-1.084v-.002z" fill="#37E1BE"></path><path d="M14.303 1.867C12.955.7 11.248 0 9.39 0 7.532 0 5.883.677 4.545 1.807 2.791 3.29 1.627 5.557 1.5 8.125v9.201c.932-2.65 3.097-6.25 6.357-8.307.5-.318 1.025-.595 1.569-.829 1.883-.801 3.878-.932 5.746-.706-.222-2.83-.718-5.002-.87-5.617h.001z" fill="#A569FF"></path><path d="M17.305 4.961a199.47 199.47 0 01-1.08-1.094c-.202-.213-.398-.419-.586-.622l-1.333-1.378c.151.615.648 2.786.869 5.617 3.288.395 6.185 1.898 7.396 2.8-1.306-1.275-3.475-3.487-5.266-5.323z" fill="#1E37FC"></path></svg>`,

            'ocrpro': `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles-icon lucide-sparkles"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"/><path d="M20 3v4"/><path d="M22 5h-4"/><path d="M4 17v2"/><path d="M5 18H3"/></svg>`,

            'utools': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 1024 1024" width="1em" xmlns="http://www.w3.org/2000/svg"><title>uTools</title><path d="M512 512m-509.449808 0a509.449808 509.449808 0 1 0 1018.899616 0 509.449808 509.449808 0 1 0-1018.899616 0Z" fill="#333333"></path><path d="M273.066667 205.682759l194.128429 103.302375a24.521073 24.521073 0 0 1 10.122299 33.152491l-4.708046 8.847203a65.127969 65.127969 0 0 1-88.079694 26.894712l-194.658084-103.577011a24.521073 24.521073 0 0 1-8.964904-34.0941l4.43341-8.317548a65.127969 65.127969 0 0 1 88.079693-26.894712z" fill="#f0f0f0"></path><path d="M149.362759 370.994023l282.090421 150.127816a67.089655 67.089655 0 0 1 26.933946 90.10023 24.521073 24.521073 0 0 1-33.15249 10.141916L144.144674 471.785441a67.089655 67.089655 0 0 1-27.71862-90.747587 24.521073 24.521073 0 0 1 32.917088-10.043831z" fill="#f0f0f0"></path><path d="M355.65364 876.559693l-2.197088-220.493486a24.521073 24.521073 0 0 1 24.266053-24.776092l9.416092-0.078467a65.127969 65.127969 0 0 1 65.775326 64.460996l2.197088 219.120306a24.521073 24.521073 0 0 1-24.28567 24.776092l-9.416092 0.098084a65.127969 65.127969 0 0 1-65.755709-63.107433z" fill="#f0f0f0"></path><path d="M575.970575 917.912031l-2.00092-319.558621a67.089655 67.089655 0 0 1 66.481533-67.285824 24.521073 24.521073 0 0 1 24.658391 24.344521l2.020536 319.166284a67.089655 67.089655 0 0 1-66.677701 67.501609 24.521073 24.521073 0 0 1-24.481839-24.167969z" fill="#f0f0f0"></path><path d="M926.111877 451.168123l-181.848275 123.664674a24.521073 24.521073 0 0 1-34.074483-6.473563l-5.296552-7.80751a65.127969 65.127969 0 0 1 17.243218-90.47295l181.985594-123.782376a24.521073 24.521073 0 0 1 34.388353 6.277395l5.296552 7.787893a65.127969 65.127969 0 0 1-17.223602 90.47295z" fill="#f0f0f0"></path><path d="M856.668199 261.002299L592.586054 440.594636a67.089655 67.089655 0 0 1-93.199694-17.73364 24.521073 24.521073 0 0 1 6.473563-34.074483l263.925211-179.494252a67.089655 67.089655 0 0 1 93.199694 17.753256 24.521073 24.521073 0 0 1-6.316629 33.956782z" fill="#f0f0f0"></path></svg>`
        };

        return icons[serviceName] || '';
    }

    // 初始化服务图标
    initServiceIcons() {
        const serviceItems = document.querySelectorAll('.service-item');
        serviceItems.forEach(item => {
            const serviceName = item.dataset.service;
            const iconElement = item.querySelector('.service-icon');
            if (iconElement && serviceName) {
                const iconSvg = this.getServiceIcon(serviceName);
                if (iconSvg) {
                    // 移除原有的CSS类
                    iconElement.className = 'service-icon';
                    // 插入SVG图标
                    iconElement.innerHTML = iconSvg;
                }
            }
        });
    }



    // 显示拖拽区域
    showDropZone() {
        let dropZone = document.getElementById('drop-zone');
        if (!dropZone) {
            dropZone = document.createElement('div');
            dropZone.id = 'drop-zone';
            dropZone.innerHTML = `
                <div class="drop-zone-content">
                    <div class="drop-zone-icon">📁</div>
                    <div class="drop-zone-text">拖拽图片到此处进行OCR识别</div>
                </div>
            `;
            dropZone.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(79, 172, 254, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 999;
                color: white;
                font-size: 18px;
                text-align: center;
            `;
            document.body.appendChild(dropZone);
        }
        dropZone.style.display = 'flex';
    }

    // 隐藏拖拽区域
    hideDropZone() {
        const dropZone = document.getElementById('drop-zone');
        if (dropZone) {
            dropZone.style.display = 'none';
        }
    }

    // 处理文件拖拽
    handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        
        if (imageFiles.length === 0) {
            this.showError('请拖拽图片文件');
            return;
        }
        
        // 处理第一个图片文件
        const file = imageFiles[0];
        const reader = new FileReader();
        reader.onload = (e) => {
            if (window.ocrPlugin) {
                window.ocrPlugin.performOCR(e.target.result);
            }
        };
        reader.readAsDataURL(file);
    }

    // 调整布局
    adjustLayout() {
        // 根据窗口大小调整布局
        const width = window.innerWidth;
        
        if (width < 600) {
            document.body.classList.add('mobile-layout');
        } else {
            document.body.classList.remove('mobile-layout');
        }
    }

    // 加载识别模式配置
    loadRecognitionModeConfigs() {
        if (!window.ocrPlugin) return;

        const modes = ['text', 'table', 'formula', 'markdown'];
        modes.forEach(mode => {
            this.loadRecognitionModeConfig(mode);
        });
    }

    // 加载单个识别模式配置
    loadRecognitionModeConfig(mode) {
        if (!window.ocrPlugin) return;

        try {
            const modeConfig = window.ocrPlugin.configManager.getRecognitionModeConfig(mode);
            const modelBtn = document.getElementById(`${mode}-model-btn`);
            const modelText = modelBtn?.querySelector('.model-text');
            const modelIcon = modelBtn?.querySelector('.model-icon');

            if (modelText) {
                // 检查是否有可用模型
                const hasAvailableModels = this.hasAvailableModels();
                
                if (modeConfig && modeConfig.service && hasAvailableModels) {
                    // 获取服务显示名称
                    const serviceName = this.getServiceDisplayName(modeConfig.service, modeConfig.model);
                    modelText.textContent = serviceName;

                    // 更新按钮中的图标
                    if (modelIcon) {
                        const iconSvg = this.getServiceIcon(modeConfig.service);
                        if (iconSvg) {
                            modelIcon.innerHTML = iconSvg;
                        } else {
                            modelIcon.innerHTML = '';
                        }
                    }
                } else {
                    // 没有可用模型或模式配置为空时显示提示
                    if (!hasAvailableModels) {
                        modelText.textContent = '请先配置模型';
                    } else {
                        modelText.textContent = '请选择模型';
                    }
                    
                    // 清空图标
                    if (modelIcon) {
                        modelIcon.innerHTML = '';
                    }
                }
            }

            // 初始化模型选择菜单
            this.initRecognitionModeModelMenu(mode);

            // 更新提示词配置按钮的可见性
            this.updatePromptConfigButtonVisibility(mode);
        } catch (error) {
            console.error(`加载识别模式配置失败 - 模式: ${mode}`, error);
            // 设置默认显示
            const modelBtn = document.getElementById(`${mode}-model-btn`);
            const modelText = modelBtn?.querySelector('.model-text');
            if (modelText) {
                modelText.textContent = '请选择模型';
            }

            // 隐藏提示词配置按钮
            this.updatePromptConfigButtonVisibility(mode, false);
        }
    }

    // 更新提示词配置按钮的可见性
    updatePromptConfigButtonVisibility(mode, forceHide = false) {
        const button = document.getElementById(`${mode}-prompt-config`);
        if (!button) return;

        if (forceHide) {
            button.style.display = 'none';
            return;
        }

        try {
            const modeConfig = window.ocrPlugin.configManager.getRecognitionModeConfig(mode);
            const isLLMService = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(modeConfig?.service);

            // 只有LLM服务才显示提示词配置按钮
            button.style.display = isLLMService ? 'flex' : 'none';
        } catch (error) {
            console.error(`更新提示词配置按钮可见性失败 - 模式: ${mode}`, error);
            button.style.display = 'none';
        }
    }

    // 初始化识别模式模型选择菜单
    initRecognitionModeModelMenu(mode) {
        const menu = document.getElementById(`${mode}-model-menu`);
        if (!menu) return;

        // 清空现有选项
        menu.innerHTML = '';

        // 获取所有可用模型（包括测试状态过滤）
        const availableModels = window.ocrPlugin.getAvailableModelsForConfig();

        if (availableModels.length === 0) {
            const option = document.createElement('div');
            option.className = 'model-option disabled';
            option.textContent = '暂无已配置的服务';
            menu.appendChild(option);
            return;
        }

        // 获取当前模式的配置，用于高亮选中项
        const currentConfig = window.ocrPlugin.configManager.getRecognitionModeConfig(mode);
        const currentService = currentConfig?.service;
        const currentModel = currentConfig?.model;

        availableModels.forEach(model => {
            const option = document.createElement('div');
            option.className = 'model-option';

            // 创建图标容器
            const iconContainer = document.createElement('div');
            iconContainer.className = 'model-option-icon';

            // 获取服务名称（用于图标）
            let serviceName;
            if (model.type === 'ocr') {
                serviceName = model.value;
            } else {
                // AI模型：解析platform:modelId格式
                const [platform] = model.value.split(':');
                serviceName = platform;
            }

            // 添加厂商图标
            const iconSvg = this.getServiceIcon(serviceName);
            if (iconSvg) {
                iconContainer.innerHTML = iconSvg;
            }

            // 创建文本容器
            const textContainer = document.createElement('div');
            textContainer.className = 'model-option-text';
            textContainer.textContent = model.name; // 移除所有特殊符号和颜色变化

            // 组装选项内容
            option.appendChild(iconContainer);
            option.appendChild(textContainer);

            // 设置数据属性
            if (model.type === 'ocr') {
                option.setAttribute('data-service', model.value);
                option.setAttribute('data-model', '');

                // 检查是否为当前选中项
                if (currentService === model.value && !currentModel) {
                    option.classList.add('selected');
                }
            } else {
                // AI模型：解析platform:modelId格式
                const [platform, modelId] = model.value.split(':');
                option.setAttribute('data-service', platform);
                option.setAttribute('data-model', modelId);

                // 检查是否为当前选中项
                if (currentService === platform && currentModel === modelId) {
                    option.classList.add('selected');
                }
            }

            option.addEventListener('click', () => {
                const service = option.getAttribute('data-service');
                const modelId = option.getAttribute('data-model');
                this.selectRecognitionModeModel(mode, service, modelId);
            });

            menu.appendChild(option);
        });
    }

    // 选择识别模式模型
    selectRecognitionModeModel(mode, service, model) {
        // 保存配置
        const result = window.ocrPlugin.configManager.setRecognitionModeConfig(mode, service, model);

        if (result.success) {
            
            // 更新主插件类中的内存配置
            window.ocrPlugin.config = result.config;

            // 更新UI显示
            const modelBtn = document.getElementById(`${mode}-model-btn`);
            const modelText = modelBtn?.querySelector('.model-text');
            const modelIcon = modelBtn?.querySelector('.model-icon');

            if (modelText) {
                const serviceName = this.getServiceDisplayName(service, model);
                modelText.textContent = serviceName;
            }

            // 更新按钮中的图标
            if (modelIcon) {
                const iconSvg = this.getServiceIcon(service);
                if (iconSvg) {
                    modelIcon.innerHTML = iconSvg;
                } else {
                    modelIcon.innerHTML = '';
                }
            }

            // 更新菜单中的选中状态
            const menu = document.getElementById(`${mode}-model-menu`);
            if (menu) {
                // 移除所有选项的选中状态
                const options = menu.querySelectorAll('.model-option');
                options.forEach(option => {
                    option.classList.remove('selected');
                });

                // 为当前选中的选项添加选中状态
                options.forEach(option => {
                    const optionService = option.getAttribute('data-service');
                    const optionModel = option.getAttribute('data-model');

                    if (optionService === service && optionModel === (model || '')) {
                        option.classList.add('selected');
                    }
                });

                // 隐藏菜单并移除按钮active状态
                menu.style.display = 'none';
                const button = document.getElementById(`${mode}-model-btn`);
                if (button) {
                    button.classList.remove('active');
                }
            }

            // 触发配置变化事件，通知主页面更新（无论是否为当前模式）
            document.dispatchEvent(new CustomEvent('configModelChanged', {
                detail: { mode: mode, service: service, model: model }
            }));

            // 如果启用了自动重新识别，触发重新识别
            if (window.ocrPlugin.shouldAutoReRecognize()) {
                window.ocrPlugin.triggerAutoReRecognize();
            }

            // 更新提示词配置按钮的可见性
            this.updatePromptConfigButtonVisibility(mode);
        } else {
            this.showError('保存配置失败: ' + result.error);
        }

        // 显示保存成功提示
        this.showSuccess(`${this.getModeDisplayName(mode)}模型已更新`);
    }

    // 获取模式显示名称
    getModeDisplayName(mode) {
        const modeNames = {
            'text': '文字识别',
            'table': '表格识别',
            'formula': '公式识别',
            'markdown': 'Markdown识别'
        };
        return modeNames[mode] || mode;
    }

    // 获取服务显示名称（简化版，只显示模型名称，图标已显示服务商）
    getServiceDisplayName(service, model) {
        const serviceNames = {
            'baidu': '百度智能云',
            'tencent': '腾讯云',
            'aliyun': '阿里云',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Gemini',
            'alibaba': '阿里云百炼',
            'bytedance': '火山引擎',
            'ocrpro': 'OCR Pro',
            'utools': 'uTools AI'
        };

        // 对于AI模型，uTools使用友好名称，其他平台使用原始API名称
        if (model && ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(service)) {
            if (service === 'utools') {
                try {
                    const ocrPlugin = this.getOcrPlugin();
                    if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                        const platformConfig = ocrPlugin.config[service];
                        const modelNameMap = platformConfig.modelNameMap || {};
                        return modelNameMap[model] || model;
                    }
                } catch (error) {
                    // 使用原始模型名称作为降级方案
                }
            }
            return model;
        }

        // 对于传统OCR服务，返回服务名称
        return serviceNames[service] || service;
    }

    // 获取服务简称（用于主界面）
    getServiceShortName(service, model) {
        // 如果没有服务，返回提示文本
        if (!service) {
            return '请先配置模型';
        }

        const serviceShortNames = {
            'baidu': '百度智能云',
            'tencent': '腾讯云',
            'aliyun': '阿里云',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Gemini',
            'alibaba': '阿里云百炼',
            'bytedance': '火山引擎',
            'ocrpro': 'OCR Pro',
            'utools': 'uTools AI'
        };

        // 如果是AI服务且提供了模型信息，uTools使用友好名称，其他平台使用原始API名称
        if (model && ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(service)) {
            let displayName = model;
            if (service === 'utools') {
                try {
                    const ocrPlugin = this.getOcrPlugin();
                    if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                        const platformConfig = ocrPlugin.config[service];
                        const modelNameMap = platformConfig.modelNameMap || {};
                        displayName = modelNameMap[model] || model;
                    }
                } catch (error) {
                    console.warn('获取uTools模型友好名称失败:', error);
                }
            }

            // 为了避免主页面按钮显示过长，对长模型名称进行截断
            if (displayName.length > 15) {
                return displayName.substring(0, 12) + '...';
            } else {
                return displayName;
            }
        }

        // 对于传统OCR服务，检查配置状态并返回合适的显示文本
        if (['baidu', 'tencent', 'aliyun'].includes(service)) {
            try {
                const ocrPlugin = this.getOcrPlugin();
                if (ocrPlugin && ocrPlugin.isServiceConfigured) {
                    const isConfigured = ocrPlugin.isServiceConfigured(service);

                    if (isConfigured) {
                        // 服务已配置，显示服务名称
                        return serviceShortNames[service] || service;
                    } else {
                        // 服务未配置，显示提示文本
                        return '去配置模型';
                    }
                } else {
                    // ocrPlugin 不可用时，回退到显示服务名称
                    return serviceShortNames[service] || service;
                }
            } catch (error) {
                console.warn('获取传统OCR服务配置失败:', error);
                // 出错时默认显示服务名称
                return serviceShortNames[service] || service;
            }
        }

        // 默认返回服务名称
        return serviceShortNames[service] || service;
    }

    // 切换配置区域
    switchConfigSection(service) {
        const sections = [
            'baidu-config', 'tencent-config', 'aliyun-config',
            'openai-config', 'anthropic-config', 'google-config',
            'alibaba-config', 'bytedance-config', 'ocrpro-config', 'utools-config'
        ];

        // 先隐藏所有配置区域
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });

        const sectionMap = {
            'baidu': 'baidu-config',
            'tencent': 'tencent-config',
            'aliyun': 'aliyun-config',
            'openai': 'openai-config',
            'anthropic': 'anthropic-config',
            'google': 'google-config',
            'alibaba': 'alibaba-config',
            'bytedance': 'bytedance-config',
            'ocrpro': 'ocrpro-config',
            'utools': 'utools-config'
        };

        // 确定要显示的配置区域
        const targetSection = sectionMap[service] || sectionMap['baidu']; // 默认回退到百度OCR

        if (targetSection) {
            const element = document.getElementById(targetSection);
            if (element) {
                element.style.display = 'block';
            } else {
                // 如果找不到目标区域，强制显示百度OCR配置
                const baiduElement = document.getElementById('baidu-config');
                if (baiduElement) {
                    baiduElement.style.display = 'block';
                }
            }
        } else {
            // 未知服务类型，显示百度OCR配置
            const baiduElement = document.getElementById('baidu-config');
            if (baiduElement) {
                baiduElement.style.display = 'block';
            }
        }
    }

    // 更新进度
    updateProgress(percentage, message) {
        let progressEl = document.getElementById('progress-bar');
        if (!progressEl) {
            progressEl = document.createElement('div');
            progressEl.id = 'progress-bar';
            progressEl.innerHTML = `
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text"></div>
                </div>
            `;
            progressEl.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.95);
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            `;
            document.body.appendChild(progressEl);
        }
        
        const fill = progressEl.querySelector('.progress-fill');
        const text = progressEl.querySelector('.progress-text');
        
        fill.style.width = `${percentage}%`;
        text.textContent = message || `${percentage}%`;
        
        if (percentage >= 100) {
            setTimeout(() => {
                if (progressEl.parentNode) {
                    document.body.removeChild(progressEl);
                }
            }, 1000);
        }
    }

    // 清理所有通知
    clearNotifications() {
        this.notifications.forEach(toast => {
            if (toast._hideTimeout) {
                clearTimeout(toast._hideTimeout);
            }
            this.hideToast(toast);
        });
        this.notifications = [];

        // 移除Toast容器
        const container = document.getElementById('toast-container');
        if (container) {
            container.remove();
        }
    }

    // 设置密码显示/隐藏切换功能
    setupPasswordToggles() {
        const toggleButtons = [
            // 传统OCR服务
            'toggle-baidu-api-key',
            'toggle-baidu-secret-key',
            'toggle-tencent-secret-id',
            'toggle-tencent-secret-key',
            'toggle-aliyun-access-key',
            'toggle-aliyun-access-secret',
            // 传统翻译服务
            'toggle-baidu-translate-api-key',
            'toggle-baidu-translate-secret-key',
            // AI平台API Key
            'toggle-openai-api-key',
            'toggle-anthropic-api-key',
            'toggle-google-api-key',
            'toggle-alibaba-api-key',
            'toggle-bytedance-api-key',
            // 火山引擎新增字段
            'toggle-bytedance-access-key',
            'toggle-bytedance-secret-key',
            // 兼容旧版本
            'toggle-api-key'
        ];

        toggleButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                // 初始化按钮状态（密码隐藏状态）
                this.initializePasswordToggleState(button);

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.togglePasswordVisibility(buttonId);
                });
            }
        });
    }

    // 初始化密码切换按钮状态
    initializePasswordToggleState(button) {
        // 设置初始状态为隐藏（密码模式）
        button.classList.remove('visible');
        button.classList.add('hidden');

        // 更新图标
        this.updatePasswordToggleIcon(button, false);
    }

    // 切换密码可见性
    togglePasswordVisibility(buttonId) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        // 获取对应的输入框ID
        const inputId = buttonId.replace('toggle-', '');
        const input = document.getElementById(inputId);

        if (!input) {
            return;
        }

        // 切换输入框类型和按钮状态
        const isCurrentlyHidden = input.type === 'password';

        if (isCurrentlyHidden) {
            // 当前是隐藏状态，切换为显示状态
            input.type = 'text';
            button.classList.remove('hidden');
            button.classList.add('visible');
            this.updatePasswordToggleIcon(button, true);
        } else {
            // 当前是显示状态，切换为隐藏状态
            input.type = 'password';
            button.classList.remove('visible');
            button.classList.add('hidden');
            this.updatePasswordToggleIcon(button, false);
        }
    }

    // 更新密码切换按钮图标
    updatePasswordToggleIcon(button, isVisible) {
        const eyeIcon = button.querySelector('.eye-icon');
        if (!eyeIcon) return;

        if (isVisible) {
            // 密码可见状态 - 显示张开的眼睛
            eyeIcon.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
            `;
            button.title = '隐藏密码';
        } else {
            // 密码隐藏状态 - 显示闭合的眼睛（带斜线）
            eyeIcon.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
            `;
            button.title = '显示密码';
        }
    }

    // 重置所有密码字段为隐藏状态
    resetAllPasswordFields() {
        const passwordInputs = [
            'baidu-api-key', 'baidu-secret-key',
            'tencent-secret-id', 'tencent-secret-key',
            'aliyun-access-key', 'aliyun-access-secret',
            'baidu-translate-app-id', 'baidu-translate-api-key', 'baidu-translate-secret-key',
            'openai-api-key', 'anthropic-api-key', 'google-api-key',
            'alibaba-api-key', 'bytedance-api-key',
            'bytedance-access-key', 'bytedance-secret-key'
        ];

        passwordInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            const toggleBtn = document.getElementById(`toggle-${inputId}`);

            if (input && input.type === 'text') {
                // 将输入框重置为密码类型
                input.type = 'password';

                // 重置切换按钮状态
                if (toggleBtn) {
                    toggleBtn.classList.remove('visible');
                    toggleBtn.classList.add('hidden');
                    this.updatePasswordToggleIcon(toggleBtn, false);
                }
            }
        });
    }

    // 设置结果控制功能
    setupResultControls() {
        // 换行符切换按钮事件
        const linebreakToggleBtn = document.getElementById('linebreak-toggle-btn');
        if (linebreakToggleBtn) {
            // 从配置中读取初始状态
            const config = window.configManager?.getConfig();
            const removeLinebreaks = config?.ui?.removeLinebreaks || false;

            // 设置初始状态（注意：removeLinebreaks为true表示去除换行符，按钮enabled为false）
            const enabled = !removeLinebreaks;
            linebreakToggleBtn.setAttribute('data-enabled', enabled.toString());
            linebreakToggleBtn.title = enabled ? '保留换行符' : '去除换行符';

            linebreakToggleBtn.addEventListener('click', async () => {
                await this.handleLinebreakToggle();
                this.saveLinebreakSetting();
            });
        }
    }

    // 处理换行符切换
    async handleLinebreakToggle() {
        const resultText = document.getElementById('result-text');
        const rawResultText = document.getElementById('raw-result-text');
        const linebreakToggleBtn = document.getElementById('linebreak-toggle-btn');

        if (!linebreakToggleBtn) return;

        // 切换按钮状态
        const currentEnabled = linebreakToggleBtn.getAttribute('data-enabled') === 'true';
        const newEnabled = !currentEnabled;
        linebreakToggleBtn.setAttribute('data-enabled', newEnabled.toString());
        linebreakToggleBtn.title = newEnabled ? '保留换行符' : '去除换行符';

        // 显示切换提示（无论是否有文本都显示）
        this.showLinebreakRemoved(!newEnabled);

        // 检查当前是单栏还是双栏模式
        const singleContainer = document.getElementById('single-result-container');
        const dualContainer = document.getElementById('dual-result-container');

        const isSingleMode = singleContainer && singleContainer.style.display !== 'none';
        const isDualMode = dualContainer && dualContainer.style.display !== 'none';

        // 获取当前文本框中的实际内容（包括用户编辑后的内容）
        let currentText = '';
        if (isSingleMode && resultText) {
            currentText = resultText.value;
        } else if (isDualMode && rawResultText) {
            currentText = rawResultText.value;
        }

        // 如果文本框为空，使用原始文本作为备选
        if (!currentText && this.originalResultText) {
            currentText = this.originalResultText;
        }

        if (!currentText) return;

        let text;
        if (!newEnabled) {
            // 去除换行符（按钮禁用状态）- 基于当前文本内容
            text = currentText.replace(/\n+/g, '').replace(/\s+/g, ' ').trim();
        } else {
            // 如果要恢复换行符，但当前文本已经被处理过，则使用原始文本
            // 如果当前文本就是原始文本或用户编辑的文本，则保持不变
            if (this.originalResultText && currentText === this.originalResultText.replace(/\n+/g, '').replace(/\s+/g, ' ').trim()) {
                // 当前文本是去除换行符后的版本，恢复原始文本
                text = this.originalResultText;
            } else {
                // 当前文本是用户编辑的或其他情况，保持当前文本
                text = currentText;
            }
        }

        // 更新相应的文本框
        if (isSingleMode && resultText) {
            resultText.value = text;
        } else if (isDualMode && rawResultText) {
            rawResultText.value = text;

            // 如果是双栏模式，还需要更新渲染结果
            const renderedResult = document.getElementById('rendered-result');
            if (renderedResult) {
                // 获取当前识别模式
                const currentMode = this.getCurrentRecognitionMode();
                await this.renderResult(text, currentMode, renderedResult);
            }
        }
    }

    // 获取当前识别模式
    getCurrentRecognitionMode() {
        // 优先从主插件获取当前识别模式，确保状态一致性
        if (window.ocrPlugin && window.ocrPlugin.currentRecognitionMode) {
            return window.ocrPlugin.currentRecognitionMode;
        }
        
        // 备用方案：从UI元素获取
        const modeText = document.querySelector('#recognition-mode-btn .mode-text');
        if (!modeText) return 'text';

        const modeNames = {
            '文字识别': 'text',
            '表格识别': 'table',
            '公式识别': 'formula',
            'MD识别': 'markdown'
        };

        return modeNames[modeText.textContent] || 'text';
    }

    // 保存换行符设置
    saveLinebreakSetting() {
        const linebreakToggleBtn = document.getElementById('linebreak-toggle-btn');
        if (!linebreakToggleBtn || !window.configManager) return;

        const config = window.configManager.getConfig();
        const enabled = linebreakToggleBtn.getAttribute('data-enabled') === 'true';
        config.ui.removeLinebreaks = !enabled; // enabled为false时表示去除换行符
        window.configManager.saveConfig(config);
    }

    // 设置主题切换功能
    setupThemeToggle() {
        // 配置界面主题切换按钮（原有的）
        const themeToggleBtn = document.getElementById('theme-toggle-btn');

        // 各个设置页面的主题切换按钮
        const historyThemeToggleBtn = document.getElementById('history-theme-toggle-btn');
        const baseConfigThemeToggleBtn = document.getElementById('base-config-theme-toggle-btn');

        // 主页面历史记录按钮
        const historyBtn = document.getElementById('history-btn');
        if (historyBtn) {
            historyBtn.addEventListener('click', () => {
                this.showHistoryView();
            });
        }

        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        if (historyThemeToggleBtn) {
            historyThemeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        if (baseConfigThemeToggleBtn) {
            baseConfigThemeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    // 切换主题（三种模式循环：自动 -> 亮色 -> 暗色 -> 自动）
    toggleTheme() {
        const currentMode = this.getThemeMode();
        let newMode, newTheme;
        
        switch (currentMode) {
            case 'auto':
                newMode = 'light';
                newTheme = 'light';
                break;
            case 'light':
                newMode = 'dark';
                newTheme = 'dark';
                break;
            case 'dark':
                newMode = 'auto';
                // 自动模式下检测uTools主题
                if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
                    try {
                        newTheme = utools.isDarkColors() ? 'dark' : 'light';
                    } catch (error) {
                        newTheme = 'light';
                    }
                } else {
                    newTheme = 'light';
                }
                break;
            default:
                newMode = 'auto';
                newTheme = 'light';
        }
        
        // 应用新主题
        document.documentElement.setAttribute('data-theme', newTheme);
        
        // 保存模式设置
        if (newMode === 'auto') {
            this.removeStorageItem('themeMode');
        } else {
            this.setStorageItem('themeMode', newMode);
        }
        

        
        // 更新主题切换按钮的图标
        this.updateThemeIcon(newTheme, newMode);
        
        // 显示提示信息
        const modeNames = {
            'auto': '自动模式',
            'light': '亮色模式',
            'dark': '暗色模式'
        };
        this.showNotification(`已切换到${modeNames[newMode]}`);
    }

    // 加载保存的主题
    loadTheme() {
        const themeMode = this.getThemeMode();
        let actualTheme;
        
        if (themeMode === 'auto') {
            // 自动模式：检测uTools主题
            if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
                try {
                    actualTheme = utools.isDarkColors() ? 'dark' : 'light';
                } catch (error) {
                    actualTheme = 'light';
                }
            } else {
                actualTheme = 'light';
            }
        } else {
            // 手动模式：使用设定的主题
            actualTheme = themeMode;
        }
        
        document.documentElement.setAttribute('data-theme', actualTheme);
        this.updateThemeIcon(actualTheme, themeMode);
    }

    // 更新主题图标
    updateThemeIcon(theme, mode) {
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const historyThemeToggleBtn = document.getElementById('history-theme-toggle-btn');
        const baseConfigThemeToggleBtn = document.getElementById('base-config-theme-toggle-btn');

        // 根据模式选择图标和标题
        let iconHTML, configIconHTML, title;
        if (mode === 'auto') {
            iconHTML = `<svg class="theme-icon auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 18a6 6 0 0 0 0-12v12z"/>
            </svg>`;
            configIconHTML = `<svg class="btn-icon auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 18a6 6 0 0 0 0-12v12z"/>
            </svg>`;
            title = `自动模式 (当前: ${theme === 'dark' ? '暗色' : '亮色'})`;
        } else if (mode === 'light') {
            iconHTML = `<svg class="theme-icon light" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="4"/>
                <path d="M12 2v2"/>
                <path d="M12 20v2"/>
                <path d="m4.93 4.93 1.41 1.41"/>
                <path d="m17.66 17.66 1.41 1.41"/>
                <path d="M2 12h2"/>
                <path d="M20 12h2"/>
                <path d="m6.34 17.66-1.41 1.41"/>
                <path d="m19.07 4.93-1.41 1.41"/>
            </svg>`;
            configIconHTML = `<svg class="btn-icon light" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="4"/>
                <path d="M12 2v2"/>
                <path d="M12 20v2"/>
                <path d="m4.93 4.93 1.41 1.41"/>
                <path d="m17.66 17.66 1.41 1.41"/>
                <path d="M2 12h2"/>
                <path d="M20 12h2"/>
                <path d="m6.34 17.66-1.41 1.41"/>
                <path d="m19.07 4.93-1.41 1.41"/>
            </svg>`;
            title = '亮色模式';
        } else if (mode === 'dark') {
            iconHTML = `<svg class="theme-icon dark" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
            </svg>`;
            configIconHTML = `<svg class="btn-icon dark" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
            </svg>`;
            title = '暗色模式';
        } else {
            // 兼容旧版本，根据实际主题显示
            if (theme === 'dark') {
                iconHTML = `<svg class="theme-icon light" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="4"/>
                    <path d="M12 2v2"/>
                    <path d="M12 20v2"/>
                    <path d="m4.93 4.93 1.41 1.41"/>
                    <path d="m17.66 17.66 1.41 1.41"/>
                    <path d="M2 12h2"/>
                    <path d="M20 12h2"/>
                    <path d="m6.34 17.66-1.41 1.41"/>
                    <path d="m19.07 4.93-1.41 1.41"/>
                </svg>`;
                configIconHTML = `<svg class="btn-icon light" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="4"/>
                    <path d="M12 2v2"/>
                    <path d="M12 20v2"/>
                    <path d="m4.93 4.93 1.41 1.41"/>
                    <path d="m17.66 17.66 1.41 1.41"/>
                    <path d="M2 12h2"/>
                    <path d="M20 12h2"/>
                    <path d="m6.34 17.66-1.41 1.41"/>
                    <path d="m19.07 4.93-1.41 1.41"/>
                </svg>`;
                title = '暗色主题';
            } else {
                iconHTML = `<svg class="theme-icon dark" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
                </svg>`;
                configIconHTML = `<svg class="btn-icon dark" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
                </svg>`;
                title = '亮色主题';
            }
        }

        if (themeToggleBtn) {
            themeToggleBtn.innerHTML = configIconHTML;
            themeToggleBtn.title = title;
        }

        if (historyThemeToggleBtn) {
            historyThemeToggleBtn.innerHTML = configIconHTML;
            historyThemeToggleBtn.title = title;
        }

        if (baseConfigThemeToggleBtn) {
            baseConfigThemeToggleBtn.innerHTML = configIconHTML;
            baseConfigThemeToggleBtn.title = title;
        }
    }

    // 设置主题监听器（监听uTools主题变化）
    setupThemeListener() {
        // 检查是否在uTools环境中
        if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
            // 记录当前uTools主题状态
            let lastUToolsTheme = null;
            
            try {
                lastUToolsTheme = utools.isDarkColors() ? 'dark' : 'light';
            } catch (error) {
                return;
            }
            
            // 定期检查uTools主题变化
            const checkThemeChange = () => {
                try {
                    const currentUToolsTheme = utools.isDarkColors() ? 'dark' : 'light';
                    
                    // 如果uTools主题发生变化
                    if (currentUToolsTheme !== lastUToolsTheme) {
                        lastUToolsTheme = currentUToolsTheme;
                        
                        // 只有在自动模式下才跟随uTools主题变化
                        const themeMode = this.getThemeMode();
                        if (themeMode === 'auto') {
                            document.documentElement.setAttribute('data-theme', currentUToolsTheme);
                            this.updateThemeIcon(currentUToolsTheme, 'auto');
                            this.showNotification(`已自动切换到${currentUToolsTheme === 'dark' ? '暗色' : '亮色'}主题`);
                        }
                    }
                } catch (error) {
                    // 静默处理错误，避免控制台噪音
                }
            };
            
            // 每2秒检查一次，减少性能开销
            setInterval(checkThemeChange, 2000);
            
            // 添加窗口焦点事件监听，确保切换回插件时能及时检测主题变化
            window.addEventListener('focus', () => {
                setTimeout(checkThemeChange, 100);
            });
        }
    }

    // 获取当前主题模式
    getThemeMode() {
        const savedMode = this.getStorageItem('themeMode');
        return savedMode || 'auto'; // 默认为自动模式
    }

    // 重置主题为自动模式（可通过快捷键或其他方式调用）
    resetThemeToAuto() {
        this.removeStorageItem('themeMode');
        
        // 重新检测并应用uTools主题
        if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
            try {
                const autoTheme = utools.isDarkColors() ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', autoTheme);
                this.updateThemeIcon(autoTheme, 'auto');
                this.showNotification('已重置为自动模式');
                return true;
            } catch (error) {
                this.showNotification('重置主题失败，请检查uTools环境', 'error');
                return false;
            }
        } else {
            this.showNotification('当前不在uTools环境中，无法自动跟随主题', 'warning');
            return false;
        }
    }

    // 获取主题状态
    getThemeStatus() {
        const mode = this.getThemeMode();
        const currentTheme = document.documentElement.getAttribute('data-theme');
        
        let utoolsTheme = null;
        if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
            try {
                utoolsTheme = utools.isDarkColors() ? 'dark' : 'light';
            } catch (error) {
                // 忽略错误
            }
        }
        
        return {
            mode: mode,
            currentTheme: currentTheme,
            utoolsTheme: utoolsTheme,
            isInSync: mode === 'auto' && currentTheme === utoolsTheme
        };
    }

    // 绑定OCR测试按钮事件
    bindOCRTestButtonEvents() {
        const testButtons = [
            { id: 'baidu-test-btn', service: 'baidu' },
            { id: 'tencent-test-btn', service: 'tencent' },
            { id: 'aliyun-test-btn', service: 'aliyun' }
        ];

        testButtons.forEach(({ id, service }) => {
            const button = document.getElementById(id);
            if (button) {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    await this.handleOCRTestClick(service, button);
                });
            }
        });
    }

    // 处理OCR测试按钮点击
    async handleOCRTestClick(service, button) {
        if (!window.ocrPlugin) {
            this.showError('OCR插件未初始化');
            return;
        }

        // 防止重复点击
        if (button.classList.contains('testing')) {
            return;
        }

        try {
            // 设置测试状态
            this.setOCRTestButtonState(button, 'testing');

            // 获取当前服务配置
            const config = window.ocrPlugin.configManager.getConfig();
            const serviceConfig = config[service];

            if (!serviceConfig) {
                throw new Error(`${this.getServiceDisplayName(service)}配置不存在`);
            }

            // 验证配置完整性
            const isConfigured = this.validateOCRServiceConfig(service, serviceConfig);
            if (!isConfigured.valid) {
                throw new Error(isConfigured.error);
            }

            // 执行连接测试
            // 构建测试配置对象，包含service字段
            const testConfig = { ...config, service: service };
            const result = await window.ocrPlugin.testServiceConnection(service, testConfig);

            if (result.success) {
                this.setOCRTestButtonState(button, 'success');
                this.showSuccess(`${this.getServiceDisplayName(service)}连接测试成功`);

                // 缓存成功状态
                const successStatus = { type: 'ready', message: '就绪' };
                const serviceConfigHash = window.ocrPlugin.generateServiceConfigHash(service, config);
                window.ocrPlugin.setCachedServiceStatus(service, successStatus, null, serviceConfigHash);

                // 更新服务状态指示器
                this.updateConfigServiceStatus(service, 'ready');
            } else {
                this.setOCRTestButtonState(button, 'error');
                this.showError(`${this.getServiceDisplayName(service)}连接测试失败: ${result.error}`);

                // 缓存失败状态
                const errorStatus = { type: 'error', message: '连接失败' };
                const serviceConfigHash = window.ocrPlugin.generateServiceConfigHash(service, config);
                window.ocrPlugin.setCachedServiceStatus(service, errorStatus, null, serviceConfigHash);

                // 更新服务状态指示器
                this.updateConfigServiceStatus(service, 'error');

                // 检查是否需要切换到其他可用服务
                this.checkAndSwitchToAvailableService(service);
            }
        } catch (error) {
            console.error(`${service} OCR测试失败:`, error);
            this.setOCRTestButtonState(button, 'error');
            this.showError(`${this.getServiceDisplayName(service)}连接测试失败: ${error.message}`);

            // 缓存错误状态
            const errorStatus = { type: 'error', message: '连接失败' };
            const config = window.ocrPlugin.configManager.getConfig();
            const serviceConfigHash = window.ocrPlugin.generateServiceConfigHash(service, config);
            window.ocrPlugin.setCachedServiceStatus(service, errorStatus, null, serviceConfigHash);

            // 更新服务状态指示器
            this.updateConfigServiceStatus(service, 'error');

            // 检查是否需要切换到其他可用服务
            this.checkAndSwitchToAvailableService(service);
        }

        // 移除自动恢复逻辑，让测试状态持久保持
        // 状态将在用户再次点击测试或修改配置时重置
    }

    // 恢复传统服务商测试按钮状态
    restoreTraditionalServiceTestButtonStates() {
        if (!window.ocrPlugin) return;

        const services = ['baidu', 'tencent', 'aliyun'];
        const testTypes = [
            { suffix: '', type: 'ocr' },
            { suffix: '-translate', type: 'translate' }
        ];

        services.forEach(service => {
            testTypes.forEach(({ suffix, type }) => {
                const buttonId = `${service}${suffix}-test-btn`;
                const button = document.getElementById(buttonId);

                if (button) {
                    // 获取缓存的服务状态
                    const cachedStatus = window.ocrPlugin.getCachedServiceStatus(service);

                    if (cachedStatus && cachedStatus.status) {
                        // 根据缓存状态设置按钮状态
                        let buttonState = 'normal';

                        if (cachedStatus.status.type === 'ready') {
                            buttonState = 'success';
                        } else if (cachedStatus.status.type === 'error') {
                            buttonState = 'error';
                        }

                        // 设置按钮状态
                        if (type === 'ocr') {
                            this.setOCRTestButtonState(button, buttonState);
                        } else {
                            this.setTranslateTestButtonState(button, buttonState);
                        }
                    }
                }
            });
        });
    }

    // 设置OCR测试按钮状态
    setOCRTestButtonState(button, state) {
        const textElement = button.querySelector('.test-btn-text');
        if (!textElement) return;

        // 检查是否是新的传统服务商测试按钮
        if (button.classList.contains('traditional-test-btn')) {
            // 清除所有状态类
            button.classList.remove('testing', 'test-status-untested', 'test-status-success', 'test-status-failed');

            switch (state) {
                case 'testing':
                    button.classList.add('testing');
                    button.disabled = true;
                    textElement.textContent = '测试中';
                    break;
                case 'success':
                    button.classList.add('test-status-success');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '测试成功';
                    break;
                case 'error':
                    button.classList.add('test-status-failed');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '测试失败';
                    break;
                case 'normal':
                default:
                    button.classList.add('test-status-untested');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '点击测试OCR连接';
                    break;
            }
        } else {
            // 原有的测试按钮逻辑
            button.classList.remove('testing', 'test-success', 'test-error');

            switch (state) {
                case 'testing':
                    button.classList.add('testing');
                    button.disabled = true;
                    textElement.textContent = '测试中';
                    break;
                case 'success':
                    button.classList.add('test-success');
                    button.disabled = false;
                    textElement.textContent = '连接成功';
                    break;
                case 'error':
                    button.classList.add('test-error');
                    button.disabled = false;
                    textElement.textContent = '连接失败';
                    break;
                case 'normal':
                default:
                    button.disabled = false;
                    textElement.textContent = '测试连接';
                    break;
            }
        }
    }

    // 验证OCR服务配置
    validateOCRServiceConfig(service, config) {
        switch (service) {
            case 'baidu':
                if (!config.apiKey || !config.secretKey) {
                    return { valid: false, error: '请填写完整的API Key和Secret Key' };
                }
                break;
            case 'tencent':
                if (!config.secretId || !config.secretKey) {
                    return { valid: false, error: '请填写完整的Secret ID和Secret Key' };
                }
                break;
            case 'aliyun':
                if (!config.accessKey || !config.accessSecret) {
                    return { valid: false, error: '请填写完整的Access Key ID和Access Key Secret' };
                }
                break;
            default:
                return { valid: false, error: '不支持的服务类型' };
        }
        return { valid: true };
    }

    // 检查并切换到可用服务
    checkAndSwitchToAvailableService(failedService) {
        if (!window.ocrPlugin) return;

        // 获取当前配置的服务
        const currentService = window.ocrPlugin.config.service;

        // 如果当前服务就是失败的服务，尝试切换到其他可用服务
        if (currentService === failedService) {
            const availableServices = window.ocrPlugin.getAvailableServices();

            // 过滤掉失败的服务
            const otherServices = availableServices.filter(service => service.value !== failedService);

            if (otherServices.length > 0) {
                // 切换到第一个可用的服务
                const newService = otherServices[0].value;
                this.showWarning(`${this.getServiceDisplayName(failedService)}连接失败，已自动切换到${this.getServiceDisplayName(newService)}`);

                // 更新配置
                window.ocrPlugin.config.service = newService;
                window.ocrPlugin.saveConfigSimple();

                // 更新UI显示
                window.ocrPlugin.updateMainInterfaceModelFromConfig();
            } else {
                this.showWarning(`${this.getServiceDisplayName(failedService)}连接失败，请检查配置或设置其他OCR服务`);
            }
        }
    }

    // 绑定翻译配置事件
    bindTranslateConfigEvents() {
        // 绑定翻译测试按钮事件
        this.bindTranslateTestButtonEvents();

        // 绑定地域选择器事件
        this.bindTranslateRegionSelectEvents();
    }

    // 绑定翻译测试按钮事件
    bindTranslateTestButtonEvents() {
        const translateTestButtons = [
            { id: 'baidu-translate-test-btn', service: 'baidu' },
            { id: 'tencent-translate-test-btn', service: 'tencent' },
            { id: 'aliyun-translate-test-btn', service: 'aliyun' }
        ];

        translateTestButtons.forEach(({ id, service }) => {
            const button = document.getElementById(id);
            if (button) {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    await this.handleTranslateTestClick(service, button);
                });
            }
        });
    }

    // 绑定翻译地域选择器事件（已移至main.js中的bindRegionSelectEvents函数）
    bindTranslateRegionSelectEvents() {
        // 地域选择器事件现在由main.js中的bindRegionSelectEvents函数处理
        // 这里保留函数以避免调用错误，但不执行任何操作
    }

    // 加载翻译地域配置
    loadTranslateRegionConfigs() {
        if (!window.ocrPlugin) return;

        const config = window.ocrPlugin.config;

        // 加载腾讯云翻译地域配置
        const tencentRegionBtn = document.getElementById('tencent-translate-region');
        const tencentRegionMenu = document.getElementById('tencent-translate-region-menu');
        if (tencentRegionBtn && tencentRegionMenu && config.tencent) {
            const tencentRegion = config.tencent.translateRegion || config.tencent.region || 'ap-beijing';

            // 更新按钮显示文本
            const regionText = tencentRegionBtn.querySelector('.region-text');
            const selectedOption = tencentRegionMenu.querySelector(`[data-value="${tencentRegion}"]`);
            if (regionText && selectedOption) {
                regionText.textContent = selectedOption.textContent;

                // 更新选中状态
                tencentRegionMenu.querySelectorAll('.region-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                selectedOption.classList.add('selected');
            }
        }

        // 加载阿里云翻译地域配置
        const aliyunRegionBtn = document.getElementById('aliyun-translate-region');
        const aliyunRegionMenu = document.getElementById('aliyun-translate-region-menu');
        if (aliyunRegionBtn && aliyunRegionMenu && config.aliyun) {
            const aliyunRegion = config.aliyun.translateRegion || config.aliyun.region || 'cn-shanghai';

            // 更新按钮显示文本
            const regionText = aliyunRegionBtn.querySelector('.region-text');
            const selectedOption = aliyunRegionMenu.querySelector(`[data-value="${aliyunRegion}"]`);
            if (regionText && selectedOption) {
                regionText.textContent = selectedOption.textContent;

                // 更新选中状态
                aliyunRegionMenu.querySelectorAll('.region-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                selectedOption.classList.add('selected');
            }
        }
    }

    // 处理翻译测试按钮点击
    async handleTranslateTestClick(service, button) {
        if (!window.OCRServices) {
            this.showError('翻译服务未初始化');
            return;
        }

        // 防止重复点击
        if (button.classList.contains('testing')) {
            return;
        }

        try {
            // 设置测试状态
            this.setTranslateTestButtonState(button, 'testing');

            // 获取当前配置
            const config = window.configManager?.getConfig();
            if (!config) {
                throw new Error('配置管理器未初始化');
            }

            // 验证翻译配置完整性
            const validation = window.configManager.validateTraditionalTranslateAPI(service);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            // 获取翻译配置
            const translateConfig = window.configManager.getTraditionalTranslateConfig(service);
            if (!translateConfig) {
                throw new Error(`${this.getServiceDisplayName(service)}翻译配置未找到`);
            }



            // 执行翻译连接测试
            const ocrServices = new window.OCRServices();
            const result = await ocrServices.testTraditionalTranslateConnection(service, translateConfig);

            if (result.success) {
                this.setTranslateTestButtonState(button, 'success');
                this.showSuccess(`${this.getServiceDisplayName(service)}翻译连接测试成功`);
            } else {
                this.setTranslateTestButtonState(button, 'error');
                this.showError(`${this.getServiceDisplayName(service)}翻译连接测试失败: ${result.error}`);
            }
        } catch (error) {
            console.error(`${service} 翻译测试失败:`, error);
            this.setTranslateTestButtonState(button, 'error');
            this.showError(`${this.getServiceDisplayName(service)}翻译连接测试失败: ${error.message}`);
        }

        // 移除自动恢复逻辑，让测试状态持久保持
        // 状态将在用户再次点击测试或修改配置时重置
    }

    // 设置翻译测试按钮状态
    setTranslateTestButtonState(button, state) {
        const textElement = button.querySelector('.test-btn-text');
        if (!textElement) return;

        // 检查是否是新的传统服务商测试按钮
        if (button.classList.contains('traditional-test-btn')) {
            // 清除所有状态类
            button.classList.remove('testing', 'test-status-untested', 'test-status-success', 'test-status-failed');

            switch (state) {
                case 'testing':
                    button.classList.add('testing');
                    button.disabled = true;
                    textElement.textContent = '测试中';
                    break;
                case 'success':
                    button.classList.add('test-status-success');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '测试成功';
                    break;
                case 'error':
                    button.classList.add('test-status-failed');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '测试失败';
                    break;
                case 'normal':
                default:
                    button.classList.add('test-status-untested');
                    button.disabled = false;
                    textElement.textContent = '测试';
                    button.title = '点击测试翻译连接';
                    break;
            }
        } else {
            // 原有的测试按钮逻辑
            button.classList.remove('testing', 'test-success', 'test-error');

            switch (state) {
                case 'testing':
                    button.classList.add('testing');
                    button.disabled = true;
                    textElement.textContent = '测试中';
                    break;
                case 'success':
                    button.classList.add('test-success');
                    button.disabled = false;
                    textElement.textContent = '连接成功';
                    break;
                case 'error':
                    button.classList.add('test-error');
                    button.disabled = false;
                    textElement.textContent = '连接失败';
                    break;
                case 'normal':
                default:
                    button.disabled = false;
                    textElement.textContent = '测试翻译连接';
                    break;
            }
        }
    }

    // ==================== 流式输出相关方法 ====================

    // 显示流式状态
    showStreamingStatus(message = '正在识别中...') {
        try {
            // 更新识别状态显示
            this.updateRecognitionStatus('processing', message);

            // 设置识别中状态的占位符
            this.setRecognizingPlaceholder();

            // 禁用重新识别按钮
            this.disableReRecognizeButton();

            // 设置流式状态标记
            this.isStreaming = true;
        } catch (error) {
            console.error('显示流式状态时出错:', error);
        }
    }

    // 隐藏流式状态
    hideStreamingStatus() {
        try {
            // 清除流式状态标记
            this.isStreaming = false;

            // 恢复默认占位符（只有在没有显示结果的情况下）
            if (!this.originalResultText) {
                this.restoreDefaultPlaceholder();
            }

            // 恢复服务状态显示
            this.restoreServiceStatus();
        } catch (error) {
            console.error('隐藏流式状态时出错:', error);
        }
    }

    // 禁用重新识别按钮
    disableReRecognizeButton() {
        try {
            const reRecognizeBtn = document.getElementById('re-recognize-btn');
            if (reRecognizeBtn) {
                reRecognizeBtn.disabled = true;
                reRecognizeBtn.classList.add('streaming-disabled');
            }
        } catch (error) {
            console.error('禁用重新识别按钮时出错:', error);
        }
    }

    // 启用重新识别按钮
    enableReRecognizeButton() {
        try {
            const reRecognizeBtn = document.getElementById('re-recognize-btn');
            if (reRecognizeBtn) {
                reRecognizeBtn.disabled = false;
                reRecognizeBtn.classList.remove('streaming-disabled');
            }
        } catch (error) {
            console.error('启用重新识别按钮时出错:', error);
        }
    }

    // 准备流式显示区域
    prepareStreamingDisplay(mode) {
        try {
            // 清空当前结果显示
            this.clearResultDisplay();

            // 根据模式准备相应的显示区域
            if (['table', 'formula', 'markdown'].includes(mode)) {
                // 双栏模式
                this.showDualColumnResult('', mode);
            } else {
                // 单栏模式
                this.showSingleColumnResult('');
            }

            // 设置流式显示标记
            this.isStreamingDisplay = true;
        } catch (error) {
            console.error('准备流式显示区域时出错:', error);
        }
    }

    // 更新流式结果
    updateStreamingResult(chunk, fullText, mode) {
        try {
            if (['table', 'formula', 'markdown'].includes(mode)) {
                // 双栏模式：更新原始结果并重新渲染
                this.updateDualColumnStreamingResult(fullText, mode);
            } else {
                // 单栏模式：直接更新文本
                this.updateSingleColumnStreamingResult(fullText);
            }
        } catch (error) {
            console.error('更新流式结果时出错:', error);
        }
    }

    // 完成流式结果显示
    completeStreamingResult(finalText, confidence, mode) {
        try {
            // 清除流式显示标记
            this.isStreamingDisplay = false;

            // 保存原始结果文本
            this.originalResultText = finalText;

            // 更新识别状态
            this.updateRecognitionStatus('success', '识别完成');

            // 显示识别成功消息
            this.showRecognitionSuccess(confidence !== null, confidence);

            // 自动复制（如果配置了）
            const config = window.configManager?.getConfig();
            if (config?.ui?.copyAfterOCR) {
                window.ocrAPI?.copyText?.(finalText);
                this.showSuccess('已自动复制到剪贴板');

                // 注意：自动复制不触发自动关闭功能，避免与用户手动复制的自动关闭冲突
            }
        } catch (error) {
            console.error('完成流式结果显示时出错:', error);
        }
    }

    // 更新单栏流式结果
    updateSingleColumnStreamingResult(text) {
        try {
            const resultText = document.getElementById('result-text');
            if (resultText) {
                resultText.value = text;
                // 自动滚动到底部
                resultText.scrollTop = resultText.scrollHeight;
            }
        } catch (error) {
            console.error('更新单栏流式结果时出错:', error);
        }
    }

    // 更新双栏流式结果
    updateDualColumnStreamingResult(text, mode) {
        try {
            // 更新原始结果文本框
            const rawResultText = document.getElementById('raw-result-text');
            if (rawResultText) {
                rawResultText.value = text;
                // 自动滚动到底部
                rawResultText.scrollTop = rawResultText.scrollHeight;
            }

            // 实时更新渲染结果
            this.updateDualColumnRendering(text, mode);
        } catch (error) {
            console.error('更新双栏流式结果时出错:', error);
        }
    }

    // 双栏模式实时渲染
    updateDualColumnRendering(text, mode) {
        try {
            const renderedResult = document.getElementById('rendered-result');
            if (renderedResult && text.trim()) {
                this.renderResult(text, mode, renderedResult);
            }
        } catch (error) {
            console.error('双栏模式实时渲染时出错:', error);
        }
    }

    // 清空结果显示区域
    clearResultDisplay() {
        try {
            // 隐藏所有结果容器
            const singleContainer = document.getElementById('single-result-container');
            const dualContainer = document.getElementById('dual-result-container');

            if (singleContainer) {
                singleContainer.style.display = 'none';
            }
            if (dualContainer) {
                dualContainer.style.display = 'none';
            }

            // 清空文本内容
            const resultText = document.getElementById('result-text');
            const rawResultText = document.getElementById('raw-result-text');
            const renderedResult = document.getElementById('rendered-result');

            if (resultText) resultText.value = '';
            if (rawResultText) rawResultText.value = '';
            if (renderedResult) renderedResult.innerHTML = '';

            // 清除原始结果文本
            this.originalResultText = '';
        } catch (error) {
            console.error('清空结果显示区域时出错:', error);
        }
    }

    // ==================== 占位符状态管理方法 ====================

    // 初始化时保存原始占位符文本
    initOriginalPlaceholders() {
        try {
            const resultText = document.getElementById('result-text');
            const rawResultText = document.getElementById('raw-result-text');
            const translateResultText = document.getElementById('translate-result-text');

            // 保存原始占位符文本（只在首次初始化时保存，避免重复覆盖）
            if (resultText && !this.originalPlaceholders.singleColumn) {
                this.originalPlaceholders.singleColumn = resultText.placeholder || '';
            }

            if (rawResultText && !this.originalPlaceholders.dualColumn) {
                this.originalPlaceholders.dualColumn = rawResultText.placeholder || '';
            }

            // 保存翻译结果区域的原始占位符
            if (translateResultText && !this.originalPlaceholders.translateResult) {
                this.originalPlaceholders.translateResult = translateResultText.placeholder || '';
            }
        } catch (error) {
            console.error('初始化原始占位符时出错:', error);
        }
    }

    // 设置识别中状态的占位符
    setRecognizingPlaceholder() {
        try {
            const resultText = document.getElementById('result-text');
            const rawResultText = document.getElementById('raw-result-text');

            // 更新单栏模式占位符
            if (resultText) {
                resultText.placeholder = '正在识别中...';
            }

            // 更新双栏模式占位符
            if (rawResultText) {
                rawResultText.placeholder = '正在识别中...';
            }
        } catch (error) {
            console.error('设置识别中占位符时出错:', error);
        }
    }

    // 恢复原始占位符
    restoreDefaultPlaceholder() {
        try {
            const resultText = document.getElementById('result-text');
            const rawResultText = document.getElementById('raw-result-text');

            // 恢复单栏模式占位符
            if (resultText) {
                resultText.placeholder = this.originalPlaceholders.singleColumn;
            }

            // 恢复双栏模式占位符
            if (rawResultText) {
                rawResultText.placeholder = this.originalPlaceholders.dualColumn;
            }
        } catch (error) {
            console.error('恢复原始占位符时出错:', error);
        }
    }

    // ==================== 翻译页面占位符管理方法 ====================

    // 设置翻译中状态的占位符
    setTranslatingPlaceholder() {
        try {
            const translateResultText = document.getElementById('translate-result-text');

            if (translateResultText) {
                translateResultText.placeholder = '正在翻译中...';
                translateResultText.classList.add('translating');
                translateResultText.classList.remove('error');
            }
        } catch (error) {
            console.error('设置翻译中占位符时出错:', error);
        }
    }

    // 恢复翻译结果区域的默认占位符
    restoreTranslateDefaultPlaceholder() {
        try {
            const translateResultText = document.getElementById('translate-result-text');

            if (translateResultText && this.originalPlaceholders.translateResult) {
                translateResultText.placeholder = this.originalPlaceholders.translateResult;
                translateResultText.classList.remove('translating', 'error');
            }
        } catch (error) {
            console.error('恢复翻译默认占位符时出错:', error);
        }
    }

    // 设置翻译失败状态的占位符
    setTranslateErrorPlaceholder(errorMessage = '翻译失败，请重试') {
        try {
            const translateResultText = document.getElementById('translate-result-text');

            if (translateResultText) {
                translateResultText.placeholder = errorMessage;
                translateResultText.classList.add('error');
                translateResultText.classList.remove('translating');
            }
        } catch (error) {
            console.error('设置翻译错误占位符时出错:', error);
        }
    }

    // 初始化快捷键配置
    initShortcutsConfig() {
        this.loadShortcutsConfig();
        this.bindShortcutsEvents();
    }

    // 加载快捷键配置数据
    loadShortcutsConfig() {
        if (!window.ocrPlugin) return;

        const shortcuts = window.ocrPlugin.configManager.getShortcuts();

        // 加载所有快捷键输入框的值
        document.querySelectorAll('.shortcut-input').forEach(input => {
            const key = input.dataset.key;
            if (key) {
                // 如果快捷键存在且不为空，显示格式化的快捷键
                if (shortcuts[key] && shortcuts[key] !== '') {
                    input.value = this.formatShortcutDisplay(shortcuts[key]);
                } else {
                    // 如果快捷键为空或不存在，显示空白
                    input.value = '';
                }
            }
        });
    }

    // 绑定快捷键配置事件
    bindShortcutsEvents() {
        // 绑定快捷键输入框事件
        document.querySelectorAll('.shortcut-input').forEach(input => {
            input.addEventListener('click', (e) => {
                this.startShortcutRecording(e.target);
            });

            input.addEventListener('keydown', (e) => {
                this.handleShortcutKeydown(e);
            });

            input.addEventListener('blur', (e) => {
                this.stopShortcutRecording(e.target);
            });
        });


    }

    // 开始快捷键录入
    startShortcutRecording(input) {
        // 清除其他输入框的录入状态
        document.querySelectorAll('.shortcut-input').forEach(inp => {
            inp.classList.remove('recording', 'conflict');
        });

        input.classList.add('recording');
        input.value = '请按下快捷键...';
        input.focus();
        this.recordingInput = input;
    }

    // 停止快捷键录入
    stopShortcutRecording(input) {
        if (input.classList.contains('recording')) {
            input.classList.remove('recording');
            // 如果没有录入有效快捷键，清空该快捷键配置
            if (input.value === '请按下快捷键...') {
                const key = input.dataset.key;
                // 清空输入框显示
                input.value = '';
                // 自动保存空值到配置中
                this.autoSaveShortcut(key, '');
            }
        }
        this.recordingInput = null;
    }

    // 处理快捷键按键事件
    handleShortcutKeydown(e) {
        if (!this.recordingInput) return;

        e.preventDefault();
        e.stopPropagation();

        // 忽略单独的修饰键
        if (['Control', 'Alt', 'Shift', 'Meta'].includes(e.key)) {
            return;
        }

        // 构建快捷键字符串
        const parts = [];
        if (e.ctrlKey || e.metaKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.shiftKey) parts.push('Shift');

        // 处理特殊键
        let key = e.key;
        if (key === ' ') key = 'Space';
        else if (key === 'Escape') key = 'Escape';
        else if (key === 'Enter') key = 'Enter';
        else if (key === 'Tab') key = 'Tab';
        else if (key === 'Backspace') key = 'Backspace';
        else if (key === 'Delete') key = 'Delete';
        else if (key.startsWith('Arrow')) key = key.replace('Arrow', '');
        else if (key.startsWith('F') && /^F\d+$/.test(key)) key = key;
        else key = key.toUpperCase();

        parts.push(key);
        const shortcut = parts.join('+');

        // 检查冲突
        const conflictKey = window.ocrPlugin.configManager.checkShortcutConflict(shortcut, this.recordingInput.dataset.key);

        this.recordingInput.value = this.formatShortcutDisplay(shortcut);
        this.recordingInput.classList.remove('recording');

        if (conflictKey) {
            this.recordingInput.classList.add('conflict');
            this.showNotification(`快捷键冲突：已被"${this.getShortcutName(conflictKey)}"使用`, 'error');
        } else {
            this.recordingInput.classList.remove('conflict');
            // 自动保存快捷键配置
            this.autoSaveShortcut(this.recordingInput.dataset.key, shortcut);
        }

        this.recordingInput.blur();
        this.recordingInput = null;
    }

    // 格式化快捷键显示
    formatShortcutDisplay(shortcut) {
        if (!shortcut) return '';
        return shortcut.replace(/\+/g, ' + ');
    }

    // 获取快捷键功能名称
    getShortcutName(key) {
        const names = {
            // 基础操作快捷键
            copyResult: '复制结果',
            takeScreenshot: '截图识别',
            reRecognize: '重新识别',
            clearResult: '清空结果',
            triggerTranslation: '触发翻译功能',

            // 页面操作
            openSettingsPage: '基础配置',
            openHistoryPage: '历史记录页面',
            openOCRPage: 'OCR页面',
            openTranslationPage: '翻译页面',
            openModelServicePage: '模型服务页面',

            // 功能切换
            switchToText: '切换到文字识别',
            switchToTable: '切换到表格识别',
            switchToFormula: '切换到公式识别',
            switchToMarkdown: '切换到Markdown识别',
            toggleTheme: '主题切换',
            toggleLineBreakMode: '换行符模式切换',
            switchTranslationModel: '翻译模型切换',
            switchHistoryCategory: '历史记录分类切换',

            // 保留原有快捷键（向后兼容）
            openSettings: '打开设置页面',
            backToMain: '返回主界面'
        };
        return names[key] || key;
    }



    // 自动保存单个快捷键
    autoSaveShortcut(key, shortcut) {
        if (!window.ocrPlugin) return;

        // 获取当前所有快捷键配置
        const currentShortcuts = window.ocrPlugin.configManager.getShortcuts();

        // 更新指定的快捷键
        currentShortcuts[key] = shortcut;

        // 保存配置
        const result = window.ocrPlugin.configManager.saveShortcuts(currentShortcuts);
        if (result.success) {
            // 重新绑定全局快捷键
            this.setupKeyboardShortcuts();
            // 根据是否为空值显示不同的提示信息
            if (shortcut === '') {
                this.showNotification('快捷键已清空', 'success');
            } else {
                this.showNotification('快捷键已保存', 'success');
            }
        } else {
            this.showNotification('保存失败: ' + result.error, 'error');
        }
    }

    // 保存快捷键配置（批量保存，用于重置功能）
    saveShortcuts() {
        if (!window.ocrPlugin) return;

        const shortcuts = {};
        let hasConflict = false;

        // 收集所有快捷键设置
        document.querySelectorAll('.shortcut-input').forEach(input => {
            const key = input.dataset.key;
            const value = input.value.replace(/ \+ /g, '+').trim();

            if (value && value !== '请按下快捷键...') {
                shortcuts[key] = value;

                // 检查是否有冲突标记
                if (input.classList.contains('conflict')) {
                    hasConflict = true;
                }
            }
        });

        if (hasConflict) {
            this.showNotification('存在快捷键冲突，请先解决冲突', 'error');
            return;
        }

        const result = window.ocrPlugin.configManager.saveShortcuts(shortcuts);
        if (result.success) {
            // 重新绑定全局快捷键
            this.setupKeyboardShortcuts();
        } else {
            this.showNotification('保存失败: ' + result.error, 'error');
        }
    }

    // 绑定网络代理按钮事件
    bindProxyButtonEvents() {
        // 绑定海外AI服务商的网络代理按钮
        const proxyButtons = [
            { id: 'openai-proxy-btn', platform: 'openai' },
            { id: 'anthropic-proxy-btn', platform: 'anthropic' },
            { id: 'google-proxy-btn', platform: 'google' }
        ];

        proxyButtons.forEach(({ id, platform }) => {
            const button = document.getElementById(id);
            if (button) {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showProxyConfigModal(platform);
                });
            }
        });

        // 绑定代理配置弹窗事件
        this.bindProxyModalEvents();
    }

    // 显示网络代理配置弹窗
    showProxyConfigModal(platform) {
        const modal = document.getElementById('proxy-config-modal');
        const title = document.getElementById('proxy-modal-title');

        if (!modal || !title) return;

        // 设置当前配置的平台
        this.currentProxyPlatform = platform;

        // 设置弹窗标题
        const platformNames = {
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Google Gemini'
        };
        title.textContent = `${platformNames[platform] || platform} - 网络代理配置`;

        // 加载当前代理配置
        this.loadProxyConfig(platform);

        // 显示弹窗
        modal.style.display = 'flex';
    }

    // 绑定代理配置弹窗事件
    bindProxyModalEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('proxy-modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideProxyConfigModal());
        }

        // 点击背景关闭
        const modal = document.getElementById('proxy-config-modal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideProxyConfigModal();
                }
            });
        }

        // 启用开关（移除表单禁用逻辑，允许用户随时配置）
        const enableSwitch = document.getElementById('proxy-enable-switch');
        if (enableSwitch) {
            enableSwitch.addEventListener('change', (e) => {
                // 更新代理按钮状态
                this.updateProxyButtonStatus(this.currentProxyPlatform);
            });
        }

        // 代理类型选择
        this.bindProxyTypeSelector();

        // 认证开关
        const authSwitch = document.getElementById('proxy-auth-enable');
        const authFields = document.getElementById('proxy-auth-fields');
        if (authSwitch && authFields) {
            authSwitch.addEventListener('change', (e) => {
                authFields.style.display = e.target.checked ? 'block' : 'none';
            });
        }

        // 密码显示切换
        const togglePassword = document.getElementById('toggle-proxy-password');
        const passwordInput = document.getElementById('proxy-password');
        if (togglePassword && passwordInput) {
            togglePassword.addEventListener('click', () => {
                const type = passwordInput.type === 'password' ? 'text' : 'password';
                passwordInput.type = type;
                togglePassword.querySelector('.eye-icon').textContent = type === 'password' ? '👁' : '🙈';
            });
        }

        // 按钮事件
        const testHeaderBtn = document.getElementById('proxy-test-header-btn');
        const saveHeaderBtn = document.getElementById('proxy-save-header-btn');

        if (testHeaderBtn) {
            testHeaderBtn.addEventListener('click', () => this.testProxyConnection());
        }
        if (saveHeaderBtn) {
            saveHeaderBtn.addEventListener('click', () => this.saveProxyConfig());
        }
    }

    // 绑定代理类型选择器
    bindProxyTypeSelector() {
        const typeBtn = document.getElementById('proxy-type-select');
        const typeMenu = document.getElementById('proxy-type-menu');
        const typeText = typeBtn?.querySelector('.proxy-type-text');

        if (!typeBtn || !typeMenu || !typeText) return;

        // 点击按钮显示/隐藏菜单
        typeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            typeMenu.classList.toggle('show');
        });

        // 点击选项
        typeMenu.addEventListener('click', (e) => {
            const option = e.target.closest('.proxy-type-option');
            if (option) {
                const value = option.dataset.value;
                typeText.textContent = option.textContent;
                typeBtn.dataset.value = value;

                // 更新选中状态
                typeMenu.querySelectorAll('.proxy-type-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                option.classList.add('selected');

                typeMenu.classList.remove('show');
            }
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            typeMenu.classList.remove('show');
        });
    }

    // 加载代理配置
    loadProxyConfig(platform) {
        // 从存储中加载代理配置
        const config = this.getStorageItem(`proxy-config-${platform}`) || {};

        // 设置表单值
        const enableSwitch = document.getElementById('proxy-enable-switch');
        const typeBtn = document.getElementById('proxy-type-select');
        const typeText = typeBtn?.querySelector('.proxy-type-text');
        const hostInput = document.getElementById('proxy-host');
        const portInput = document.getElementById('proxy-port');
        const authSwitch = document.getElementById('proxy-auth-enable');
        const authFields = document.getElementById('proxy-auth-fields');
        const usernameInput = document.getElementById('proxy-username');
        const passwordInput = document.getElementById('proxy-password');

        if (enableSwitch) {
            enableSwitch.checked = config.enabled || false;
        }

        if (typeText && typeBtn) {
            const type = config.type || 'http';
            typeText.textContent = type.toUpperCase();
            typeBtn.dataset.value = type;
        }

        if (hostInput) hostInput.value = config.host || '';
        if (portInput) portInput.value = config.port || '';

        if (authSwitch) {
            authSwitch.checked = config.auth || false;
            if (authFields) {
                authFields.style.display = config.auth ? 'block' : 'none';
            }
        }

        if (usernameInput) usernameInput.value = config.username || '';
        if (passwordInput) passwordInput.value = config.password || '';

        // 重置状态
        this.updateProxyStatus('unknown', '未测试');

        // 更新按钮状态
        this.updateProxyButtonStatus(platform);
    }

    // 隐藏代理配置弹窗
    hideProxyConfigModal() {
        const modal = document.getElementById('proxy-config-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 测试代理连接
    async testProxyConnection() {
        const config = this.getProxyConfigFromForm();

        if (!config.host || !config.port) {
            this.showNotification('请填写代理服务器地址和端口', 'warning');
            return;
        }

        this.updateProxyStatus('testing', '正在测试...');
        this.saveProxyStatus(this.currentProxyPlatform, 'testing');

        try {
            // 这里只是UI演示，实际的代理测试逻辑需要在后续实现
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 模拟测试结果
            const success = Math.random() > 0.3; // 70%成功率用于演示

            if (success) {
                this.updateProxyStatus('connected', '连接成功');
                this.saveProxyStatus(this.currentProxyPlatform, 'connected');
                this.showNotification('代理连接测试成功', 'success');
            } else {
                this.updateProxyStatus('failed', '连接失败');
                this.saveProxyStatus(this.currentProxyPlatform, 'failed');
                this.showNotification('代理连接测试失败，请检查配置', 'error');
            }
        } catch (error) {
            this.updateProxyStatus('failed', '测试出错');
            this.saveProxyStatus(this.currentProxyPlatform, 'failed');
            this.showNotification('代理连接测试出错: ' + error.message, 'error');
        }
    }

    // 保存代理配置
    saveProxyConfig() {
        const config = this.getProxyConfigFromForm();
        const platform = this.currentProxyPlatform;

        if (!platform) {
            this.showNotification('保存失败：未知平台', 'error');
            return;
        }

        // 保存到存储
        this.setStorageItem(`proxy-config-${platform}`, config);

        // 更新按钮状态
        this.updateProxyButtonStatus(platform);

        this.showNotification(`${platform.toUpperCase()} 代理配置已保存`, 'success');
        this.hideProxyConfigModal();
    }

    // 从表单获取代理配置
    getProxyConfigFromForm() {
        const enableSwitch = document.getElementById('proxy-enable-switch');
        const typeBtn = document.getElementById('proxy-type-select');
        const hostInput = document.getElementById('proxy-host');
        const portInput = document.getElementById('proxy-port');
        const authSwitch = document.getElementById('proxy-auth-enable');
        const usernameInput = document.getElementById('proxy-username');
        const passwordInput = document.getElementById('proxy-password');

        return {
            enabled: enableSwitch?.checked || false,
            type: typeBtn?.dataset.value || 'http',
            host: hostInput?.value.trim() || '',
            port: portInput?.value.trim() || '',
            auth: authSwitch?.checked || false,
            username: usernameInput?.value.trim() || '',
            password: passwordInput?.value.trim() || ''
        };
    }

    // 更新代理状态显示
    updateProxyStatus(status, text) {
        const statusElement = document.getElementById('proxy-status');
        const statusText = statusElement?.querySelector('.status-text');

        if (statusElement && statusText) {
            statusElement.className = `proxy-status ${status}`;
            statusText.textContent = text;
        }
    }

    // 初始化代理按钮状态
    initProxyButtonStates() {
        const platforms = ['openai', 'anthropic', 'google'];
        platforms.forEach(platform => {
            this.updateProxyButtonStatus(platform);
        });
    }

    // 更新代理按钮状态
    updateProxyButtonStatus(platform) {
        if (!platform) return;

        const button = document.getElementById(`${platform}-proxy-btn`);
        if (!button) return;

        // 获取代理配置
        const config = this.getStorageItem(`proxy-config-${platform}`) || {};
        const proxyStatus = this.getStorageItem(`proxy-status-${platform}`) || 'unknown';

        // 移除所有状态类
        button.classList.remove('proxy-btn-default', 'proxy-btn-warning', 'proxy-btn-success', 'proxy-btn-error');

        // 根据配置和状态设置按钮样式
        if (!config.enabled) {
            // 未开启代理 - 默认状态
            button.classList.add('proxy-btn-default');
        } else if (!config.host || !config.port) {
            // 已启用代理但未配置 - 警告状态
            button.classList.add('proxy-btn-warning');
        } else {
            // 已启用代理且已配置 - 根据连接状态显示
            switch (proxyStatus) {
                case 'connected':
                    button.classList.add('proxy-btn-success');
                    break;
                case 'failed':
                    button.classList.add('proxy-btn-error');
                    break;
                case 'testing':
                    button.classList.add('proxy-btn-warning');
                    break;
                default:
                    button.classList.add('proxy-btn-warning');
                    break;
            }
        }
    }

    // 保存代理状态
    saveProxyStatus(platform, status) {
        if (platform) {
            this.setStorageItem(`proxy-status-${platform}`, status);
            this.updateProxyButtonStatus(platform);
        }
    }

    // 显示翻译模型选择弹窗
    showTranslateModelSelectModal() {
        const modal = document.getElementById('translate-model-select-modal');
        const listContainer = document.getElementById('translate-model-select-list');

        if (!modal || !listContainer) return;

        // 清空现有列表
        listContainer.innerHTML = '';

        // 按照服务商顺序获取所有可用的翻译服务（传统服务和AI模型混合排序）
        const allTranslateServices = [];

        // 获取服务商顺序配置
        const serviceOrder = window.ocrPlugin?.configManager?.getServiceOrder() || [
            'baidu', 'tencent', 'aliyun', 'openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'
        ];

        // 按照服务商顺序处理每个服务
        serviceOrder.forEach(serviceName => {
            // 处理传统翻译服务
            if (['baidu', 'tencent', 'aliyun'].includes(serviceName)) {
                const traditionalServices = window.ocrPlugin.configManager.getAvailableTraditionalTranslateServices();
                const matchingService = traditionalServices.find(service => service.service === serviceName);
                if (matchingService) {
                    allTranslateServices.push({
                        value: matchingService.service,
                        name: matchingService.name,
                        type: 'traditional'
                    });
                }
            }
            // 处理AI模型服务
            else if (['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'].includes(serviceName)) {
                const availableModels = window.ocrPlugin.getAvailableModelsForConfig(false);
                const platformModels = availableModels.filter(model => {
                    if (model.value.includes(':')) {
                        const [platform] = model.value.split(':');
                        return platform === serviceName;
                    }
                    return false;
                });
                allTranslateServices.push(...platformModels);
            }
        });

        if (allTranslateServices.length === 0) {
            listContainer.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无可用的翻译服务</div>';
        } else {
            // 获取已添加的翻译模型列表，用于高亮显示
            const translateModels = window.ocrPlugin.configManager.getTranslateModels();
            const addedModelKeys = translateModels.map(m => `${m.service}:${m.model}`);

            // 按照服务商顺序渲染所有翻译服务（传统服务和AI模型混合排序）
            // 由于getAvailableModelsForConfig已经按服务商顺序排序，这里直接按顺序渲染即可
            allTranslateServices.forEach(model => {
                let modelItem;

                if (model.type === 'traditional') {
                    // 传统翻译服务
                    modelItem = this.createTraditionalTranslateServiceItem(model.value, model.name);
                } else if (model.type === 'ai') {
                    // AI模型
                    const [platform, modelId] = model.value.split(':');
                    const isAdded = addedModelKeys.includes(model.value);
                    modelItem = this.createTranslateModelSelectItem(platform, modelId, model.name, isAdded);
                }

                if (modelItem) {
                    listContainer.appendChild(modelItem);
                }
            });
        }

        // 显示弹窗
        modal.style.display = 'flex';
    }

    // 创建翻译模型选择项
    createTranslateModelSelectItem(service, model, displayName, isAdded = false) {
        const item = document.createElement('div');
        item.className = `fetch-model-item${isAdded ? ' added' : ''}`;

        // 获取服务图标
        const iconSvg = this.getServiceIcon(service);

        // 创建模型名称容器，包含图标和名称
        const nameContainer = document.createElement('div');
        nameContainer.className = 'fetch-model-name';
        nameContainer.style.display = 'flex';
        nameContainer.style.alignItems = 'center';
        nameContainer.style.gap = '12px';

        // 添加服务商图标
        if (iconSvg) {
            const iconDiv = document.createElement('div');
            iconDiv.style.width = '20px';
            iconDiv.style.height = '20px';
            iconDiv.style.display = 'flex';
            iconDiv.style.alignItems = 'center';
            iconDiv.style.justifyContent = 'center';
            iconDiv.style.flexShrink = '0';
            iconDiv.innerHTML = iconSvg;

            // 设置图标颜色 - 在已添加状态下使用白色，否则使用默认颜色
            const svgElement = iconDiv.querySelector('svg');
            if (svgElement) {
                svgElement.style.width = '20px';
                svgElement.style.height = '20px';
                if (isAdded) {
                    svgElement.style.color = 'white';
                    svgElement.style.fill = 'white';
                } else {
                    svgElement.style.color = 'var(--text-primary)';
                    svgElement.style.fill = 'currentColor';
                }
            }

            nameContainer.appendChild(iconDiv);
        }

        // 添加模型名称
        const nameSpan = document.createElement('span');
        nameSpan.textContent = displayName;
        nameSpan.style.flex = '1';
        nameContainer.appendChild(nameSpan);

        // 获取模型能力并添加功能图标
        // 构建模型数据对象，包含必要的信息用于能力检测
        const modelData = this.buildModelDataForCapabilityDetection(service, model, displayName);
        const capabilities = this.getModelCapabilities(service, model, modelData);
        const capabilityIconsHtml = this.generateCapabilityIcons(capabilities);
        if (capabilityIconsHtml) {
            const capabilityDiv = document.createElement('div');
            capabilityDiv.innerHTML = capabilityIconsHtml;
            nameContainer.appendChild(capabilityDiv);
        }

        // 创建按钮
        const button = document.createElement('button');
        button.type = 'button';
        button.title = isAdded ? '从列表中移除' : '添加到列表';

        if (isAdded) {
            button.className = 'remove-model-btn';
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"/>
                </svg>
            `;
        } else {
            button.className = 'add-model-btn';
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 5v14"/>
                    <path d="M5 12h14"/>
                </svg>
            `;
        }

        // 添加按钮点击事件
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            if (isAdded) {
                this.removeTranslateModel(service, model, true); // 传递fromModal=true
            } else {
                this.selectTranslateModel(service, model, displayName);
            }
        });

        // 组装元素
        item.appendChild(nameContainer);
        item.appendChild(button);

        return item;
    }

    // 创建传统翻译服务选择项
    createTraditionalTranslateServiceItem(service, displayName) {
        const item = document.createElement('div');
        item.className = 'fetch-model-item';

        // 检查传统翻译服务是否已添加到翻译模型列表
        const translateModels = window.ocrPlugin.configManager.getTranslateModels();
        const isAdded = translateModels.some(m => m.service === service && m.model === 'traditional');

        if (isAdded) {
            item.classList.add('added');
        }

        // 获取服务图标
        const iconSvg = this.getServiceIcon(service);

        // 创建服务名称容器，包含图标和名称
        const nameContainer = document.createElement('div');
        nameContainer.className = 'fetch-model-name';
        nameContainer.style.display = 'flex';
        nameContainer.style.alignItems = 'center';
        nameContainer.style.gap = '12px';

        // 添加服务商图标
        if (iconSvg) {
            const iconDiv = document.createElement('div');
            iconDiv.style.width = '20px';
            iconDiv.style.height = '20px';
            iconDiv.style.display = 'flex';
            iconDiv.style.alignItems = 'center';
            iconDiv.style.justifyContent = 'center';
            iconDiv.style.flexShrink = '0';
            iconDiv.innerHTML = iconSvg;

            // 设置图标颜色 - 在已添加状态下使用白色，否则使用默认颜色
            const svgElement = iconDiv.querySelector('svg');
            if (svgElement) {
                svgElement.style.width = '20px';
                svgElement.style.height = '20px';
                if (isAdded) {
                    svgElement.style.color = 'white';
                    svgElement.style.fill = 'white';
                } else {
                    svgElement.style.color = 'var(--text-primary)';
                    svgElement.style.fill = 'currentColor';
                }
            }

            nameContainer.appendChild(iconDiv);
        }

        // 添加服务名称
        const nameSpan = document.createElement('span');
        nameSpan.textContent = displayName;
        nameSpan.style.flex = '1';
        nameContainer.appendChild(nameSpan);

        // 添加翻译服务标签（使用与能力图标相似的样式）
        const tagDiv = document.createElement('div');
        tagDiv.className = 'capability-icons';
        tagDiv.innerHTML = `
            <span class="capability-icon-wrapper" data-capability="translate" style="color: #f59e0b;" title="传统翻译服务">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="capability-icon translate-icon">
                    <path d="M5 8l6 6"/>
                    <path d="M4 14l6-6 2-3"/>
                    <path d="M2 5h12"/>
                    <path d="M7 2h1"/>
                    <path d="M22 22l-5-10-5 10"/>
                    <path d="M14 18h6"/>
                </svg>
            </span>
        `;
        nameContainer.appendChild(tagDiv);

        // 创建添加/删除按钮
        const button = document.createElement('button');
        button.type = 'button';
        button.title = isAdded ? '从列表中移除' : '添加到列表';

        if (isAdded) {
            button.className = 'remove-model-btn';
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"/>
                </svg>
            `;
        } else {
            button.className = 'add-model-btn';
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 5v14"/>
                    <path d="M5 12h14"/>
                </svg>
            `;
        }

        // 添加按钮点击事件
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            if (isAdded) {
                this.removeTraditionalTranslateService(service, displayName, true); // 传递fromModal=true
            } else {
                this.selectTraditionalTranslateService(service, displayName);
            }
        });

        // 组装元素
        item.appendChild(nameContainer);
        item.appendChild(button);

        return item;
    }

    // 选择传统翻译服务
    selectTraditionalTranslateService(service, displayName) {
        // 添加传统翻译服务到翻译模型列表
        const result = window.ocrPlugin.configManager.addTranslateModel(service, 'traditional');

        if (result.success) {
            // 更新主插件配置
            window.ocrPlugin.config = result.config;

            // 刷新翻译模型列表显示
            this.refreshTranslateModelList();

            // 刷新弹窗显示而不是关闭
            this.showTranslateModelSelectModal();

            // 显示成功提示
            this.showNotification(`已添加翻译服务：${displayName}`, 'success');
        } else {
            this.showNotification('添加失败：' + result.error, 'error');
        }
    }

    // 移除传统翻译服务
    removeTraditionalTranslateService(service, displayName, fromModal = false) {
        // 从翻译模型列表中移除传统翻译服务
        const result = window.ocrPlugin.configManager.removeTranslateModel(service, 'traditional');

        if (result.success) {
            // 更新主插件配置
            window.ocrPlugin.config = result.config;

            // 刷新翻译模型列表显示
            this.refreshTranslateModelList();

            // 如果是从弹窗中调用，刷新弹窗显示
            if (fromModal) {
                this.showTranslateModelSelectModal();
            }

            // 显示成功提示
            this.showNotification(`已移除翻译服务：${displayName}`, 'success');

            // 同步更新翻译模型选择器
            this.updateTranslateModelSelector();
        } else {
            this.showNotification('移除失败：' + result.error, 'error');
        }
    }

    // 选择翻译模型
    selectTranslateModel(service, model, displayName) {
        // 添加到翻译模型列表
        const result = window.ocrPlugin.configManager.addTranslateModel(service, model);

        if (result.success) {
            // 更新主插件配置
            window.ocrPlugin.config = result.config;

            // 刷新翻译模型列表显示
            this.refreshTranslateModelList();

            // 刷新弹窗显示而不是关闭
            this.showTranslateModelSelectModal();

            // 显示成功提示
            this.showNotification(`已添加翻译模型：${displayName}`, 'success');
        } else {
            this.showNotification('添加失败：' + result.error, 'error');
        }
    }

    // 隐藏翻译模型选择弹窗
    hideTranslateModelSelectModal() {
        const modal = document.getElementById('translate-model-select-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 刷新翻译模型列表显示
    refreshTranslateModelList() {
        const listContainer = document.getElementById('translate-model-list');
        if (!listContainer) return;

        // 获取翻译模型列表
        const translateModels = window.ocrPlugin.configManager.getTranslateModels();

        // 使用与AI模型相同的渲染方式
        if (translateModels.length === 0) {
            listContainer.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 12px;">暂无翻译模型，点击"添加"按钮添加模型</div>';
        } else {
            // 生成模型列表HTML
            const modelsHtml = translateModels.map(modelConfig => {
                const service = modelConfig.service;
                const model = modelConfig.model || '';

                // 获取服务图标
                const iconSvg = this.getServiceIcon(service);

                // 获取显示名称和能力图标
                let displayName;
                let capabilityIconsHtml = '';

                if (model === 'traditional') {
                    // 传统翻译服务
                    displayName = window.ocrPlugin.configManager.getServiceDisplayName(service);
                    // 为传统翻译服务生成翻译标签
                    capabilityIconsHtml = `
                        <div class="capability-icons">
                            <span class="capability-icon-wrapper" data-capability="translate" style="color: #f59e0b;" title="传统翻译服务">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="capability-icon translate-icon">
                                    <path d="M5 8l6 6"/>
                                    <path d="M4 14l6-6 2-3"/>
                                    <path d="M2 5h12"/>
                                    <path d="M7 2h1"/>
                                    <path d="M22 22l-5-10-5 10"/>
                                    <path d="M14 18h6"/>
                                </svg>
                            </span>
                        </div>
                    `;
                } else if (['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'utools'].includes(service)) {
                    // AI模型，对于uTools使用友好名称，其他平台使用原始API名称
                    if (service === 'utools') {
                        try {
                            const ocrPlugin = window.ocrPlugin;
                            if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                                const platformConfig = ocrPlugin.config[service];
                                const modelNameMap = platformConfig.modelNameMap || {};
                                displayName = modelNameMap[model] || model;
                            } else {
                                displayName = model;
                            }
                        } catch (error) {
                            displayName = model;
                        }
                    } else {
                        displayName = model;
                    }
                    // 获取AI模型能力并生成功能图标
                    const modelData = this.buildModelDataForCapabilityDetection(service, model, displayName);
                    const capabilities = this.getModelCapabilities(service, model, modelData);
                    capabilityIconsHtml = this.generateCapabilityIcons(capabilities);
                } else {
                    // 其他服务
                    displayName = this.getServiceDisplayName(service, model);
                    const modelData = this.buildModelDataForCapabilityDetection(service, model, displayName);
                    const capabilities = this.getModelCapabilities(service, model, modelData);
                    capabilityIconsHtml = this.generateCapabilityIcons(capabilities);
                }

                // 创建图标HTML，确保SVG样式正确
                let iconHtml = '';
                if (iconSvg) {
                    // 创建一个临时div来处理SVG
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = iconSvg;
                    const svgElement = tempDiv.querySelector('svg');
                    if (svgElement) {
                        svgElement.style.width = '16px';
                        svgElement.style.height = '16px';
                        svgElement.style.color = 'var(--text-primary)';
                        iconHtml = `<div style="width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">${svgElement.outerHTML}</div>`;
                    }
                }

                return `
                    <div class="model-item" data-model="${model}">
                        <div class="model-name" title="${displayName}" style="display: flex; align-items: center; gap: 8px;">
                            ${iconHtml}
                            <span style="flex: 1;">${displayName}</span>
                            ${capabilityIconsHtml}
                        </div>
                        <div class="model-item-actions">
                            <button type="button" class="model-delete-btn" onclick="window.ocrPlugin.uiManager.removeTranslateModel('${service}', '${model}')" title="删除模型">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M5 12h14"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            listContainer.innerHTML = modelsHtml;

            // 启用拖拽排序功能
            this.enableTranslateModelDragSort(listContainer);
        }

        // 同步更新翻译模型选择器
        this.updateTranslateModelSelector();
    }

    // 启用翻译模型列表拖拽排序
    enableTranslateModelDragSort(listContainer) {
        if (!listContainer || !window.ocrPlugin) return;
        window.ocrPlugin.enableDragSort(listContainer, 'translateModel');
    }

    // 删除翻译模型
    removeTranslateModel(service, model, fromModal = false) {
        const result = window.ocrPlugin.configManager.removeTranslateModel(service, model);

        if (result.success) {
            // 更新主插件配置
            window.ocrPlugin.config = result.config;

            // 刷新翻译模型列表显示
            this.refreshTranslateModelList();

            // 只有在弹窗中删除时才刷新弹窗显示
            if (fromModal) {
                this.showTranslateModelSelectModal();
            }

            // 显示成功提示
            const displayName = this.getServiceDisplayName(service, model);
            this.showNotification(`已删除翻译模型：${displayName}`, 'success');
        } else {
            this.showNotification('删除失败：' + result.error, 'error');
        }
    }

    // 刷新主页面模型下拉菜单
    refreshModelDropdown() {
        try {
            // 获取主页面的模型选择下拉菜单
            const modelSelect = document.getElementById('model-select');
            if (!modelSelect) return;

            // 获取当前选中的模型
            const currentValue = modelSelect.value;

            // 重新填充下拉菜单选项
            const availableModels = window.ocrPlugin.getAvailableModelsForConfig();

            // 清空现有选项
            modelSelect.innerHTML = '';

            // 添加新选项
            availableModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.value;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });

            // 尝试恢复之前选中的模型
            if (currentValue && availableModels.some(m => m.value === currentValue)) {
                modelSelect.value = currentValue;
            } else if (availableModels.length > 0) {
                // 如果之前的模型不存在，选择第一个可用模型
                modelSelect.value = availableModels[0].value;
            }

            // 触发change事件以更新相关UI
            modelSelect.dispatchEvent(new Event('change'));
        } catch (error) {
            console.error('刷新模型下拉菜单失败:', error);
        }
    }

    // 初始化翻译模型选择器
    initTranslateModelSelector() {
        // 获取可用的翻译模型
        const translateModels = window.ocrPlugin.configManager.getTranslateModels();

        // 如果没有选中的模型，尝试从配置中恢复
        if (this.selectedTranslateModels.length === 0) {
            // 尝试恢复保存的模型选择
            const restored = this.loadSelectedTranslateModels();

            // 如果没有恢复成功且有可用模型，使用默认逻辑（选择第一个）
            if (!restored && translateModels.length > 0) {
                const firstModel = translateModels[0];
                this.selectedTranslateModels = [{
                    service: firstModel.service,
                    model: firstModel.model,
                    type: firstModel.model === 'traditional' ? 'traditional' : 'ai',
                    name: firstModel.model === 'traditional' ?
                        window.ocrPlugin.configManager.getServiceDisplayName(firstModel.service) :
                        this.getServiceShortName(firstModel.service)
                }];

                // 设置当前显示的模型
                this.currentDisplayModel = this.getModelKey(firstModel.service, firstModel.model);
            }
        }

        this.renderTranslateModelSelector();
        this.renderModelTabs();
    }

    // 生成模型唯一标识
    getModelKey(service, model) {
        return `${service}:${model}`;
    }

    // 检查模型是否已选中
    isModelSelected(service, model, type) {
        return this.selectedTranslateModels.some(selectedModel =>
            selectedModel.service === service &&
            selectedModel.model === model &&
            selectedModel.type === type
        );
    }

    // 渲染翻译模型选择器
    renderTranslateModelSelector() {
        const selectorContainer = document.getElementById('translate-model-selector');
        if (!selectorContainer) return;

        // 获取用户在基础配置页面中设置的翻译模型列表（包括AI模型和传统翻译服务）
        const translateModels = window.ocrPlugin.configManager.getTranslateModels();

        // 转换为统一格式
        const allTranslateServices = [];

        // 处理所有已添加的翻译模型
        translateModels.forEach(modelConfig => {
            if (modelConfig.model === 'traditional') {
                // 传统翻译服务
                allTranslateServices.push({
                    service: modelConfig.service,
                    model: modelConfig.model,
                    name: window.ocrPlugin.configManager.getServiceDisplayName(modelConfig.service),
                    type: 'traditional'
                });
            } else {
                // AI翻译模型
                allTranslateServices.push({
                    service: modelConfig.service,
                    model: modelConfig.model,
                    name: this.getServiceShortName(modelConfig.service),
                    type: 'ai'
                });
            }
        });

        // 如果没有可用的翻译服务，清空容器
        if (allTranslateServices.length === 0) {
            selectorContainer.innerHTML = '';
            return;
        }

        // 显示前4个服务
        const servicesToShow = allTranslateServices.slice(0, 4);

        // 生成模型按钮HTML
        const buttonsHtml = servicesToShow.map(serviceConfig => {
            const service = serviceConfig.service;
            const model = serviceConfig.model;
            const type = serviceConfig.type;

            // 检查是否为选中的模型（支持多选）
            const isActive = this.isModelSelected(service, model, type);

            // 获取服务图标
            const iconSvg = this.getServiceIcon(service);
            const iconHtml = iconSvg ? `<div class="model-icon">${iconSvg}</div>` : '';

            // 获取服务显示名称
            let serviceName = serviceConfig.name;
            
            // 对于AI模型，uTools使用友好名称，其他平台使用原始API名称（如果长度合适）
            if (type === 'ai') {
                let displayName = model;
                if (service === 'utools') {
                    try {
                        const ocrPlugin = window.ocrPlugin;
                        if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                            const platformConfig = ocrPlugin.config[service];
                            const modelNameMap = platformConfig.modelNameMap || {};
                            displayName = modelNameMap[model] || model;
                        }
                    } catch (error) {
                        // 使用原始模型名称作为降级方案
                    }
                }
                if (displayName.length <= 10) {
                    serviceName = displayName;
                }
            }

            // 为tooltip生成友好的显示文本
            let titleText;
            if (type === 'traditional') {
                titleText = serviceConfig.name;
            } else {
                // 对于AI模型，uTools使用友好名称，其他平台使用原始API名称
                let modelDisplayName = model;
                if (service === 'utools') {
                    try {
                        const ocrPlugin = window.ocrPlugin;
                        if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[service]) {
                            const platformConfig = ocrPlugin.config[service];
                            const modelNameMap = platformConfig.modelNameMap || {};
                            modelDisplayName = modelNameMap[model] || model;
                        }
                    } catch (error) {
                        modelDisplayName = model;
                    }
                }
                titleText = `${serviceConfig.name} - ${modelDisplayName}`;
            }

            return `
                <button type="button"
                        class="translate-model-btn ${isActive ? 'active' : ''}"
                        data-service="${service}"
                        data-model="${model}"
                        data-type="${type}"
                        title="${titleText}"
                        onclick="window.ocrPlugin.uiManager.toggleTranslateModel('${service}', '${model}', '${type}')">
                    ${iconHtml}
                    <span class="model-name">${serviceName}</span>
                </button>
            `;
        }).join('');

        selectorContainer.innerHTML = buttonsHtml;
    }

    // 切换翻译模型选择状态（支持多选）
    toggleTranslateModel(service, model, type = 'ai') {
        const modelKey = this.getModelKey(service, model);
        const isCurrentlySelected = this.isModelSelected(service, model, type);

        if (isCurrentlySelected) {
            // 如果已选中，则取消选择（但至少保留一个模型）
            if (this.selectedTranslateModels.length > 1) {
                this.selectedTranslateModels = this.selectedTranslateModels.filter(
                    selectedModel => !(selectedModel.service === service &&
                                     selectedModel.model === model &&
                                     selectedModel.type === type)
                );

                // 如果取消的是当前显示的模型，切换到第一个选中的模型
                if (this.currentDisplayModel === modelKey) {
                    this.currentDisplayModel = this.getModelKey(
                        this.selectedTranslateModels[0].service,
                        this.selectedTranslateModels[0].model
                    );
                    this.switchDisplayModel(this.currentDisplayModel);
                }

                this.showNotification(`已取消选择模型`, 'success', 1000);
            } else {
                this.showNotification('至少需要选择一个翻译模型', 'warning', 1500);
                return;
            }
        } else {
            // 如果未选中，则添加到选中列表
            const modelName = type === 'traditional' ?
                window.ocrPlugin.configManager.getServiceDisplayName(service) :
                this.getServiceShortName(service);

            this.selectedTranslateModels.push({
                service: service,
                model: model,
                type: type,
                name: modelName
            });

            this.showNotification(`已选择模型：${modelName}`, 'success', 1000);
        }

        // 保存用户的模型选择到配置中
        this.saveSelectedTranslateModels();

        // 重新渲染选择器和模型标签
        this.renderTranslateModelSelector();
        this.renderModelTabs();

        // 检查是否有输入文本，如果有且启用了自动重新翻译，则自动执行翻译
        const config = window.ocrPlugin.configManager.getConfig();
        const autoReTranslate = config.ui?.autoReOcr || false;
        if (autoReTranslate) {
            const sourceText = document.getElementById('translate-source-text');
            if (sourceText && sourceText.value.trim()) {
                // 延迟执行翻译，让用户看到切换提示
                setTimeout(() => {
                    this.executeTranslation();
                }, 800);
            }
        }
    }

    // 保存用户选择的翻译模型到配置中
    saveSelectedTranslateModels() {
        try {
            const config = window.ocrPlugin.configManager.getConfig();

            // 保存选中的模型列表
            config.selectedTranslateModels = this.selectedTranslateModels.map(model => ({
                service: model.service,
                model: model.model,
                type: model.type,
                name: model.name
            }));

            // 保存配置
            const result = window.ocrPlugin.configManager.saveConfig(config);
            if (!result.success) {
                console.error('保存翻译模型选择失败:', result.error);
            }
        } catch (error) {
            console.error('保存翻译模型选择异常:', error);
        }
    }

    // 从配置中恢复用户选择的翻译模型
    loadSelectedTranslateModels() {
        try {
            const config = window.ocrPlugin.configManager.getConfig();

            if (config.selectedTranslateModels && Array.isArray(config.selectedTranslateModels)) {
                // 获取当前可用的翻译模型列表
                const availableModels = window.ocrPlugin.configManager.getTranslateModels();

                // 过滤出仍然可用的模型
                const validSelectedModels = config.selectedTranslateModels.filter(savedModel => {
                    return availableModels.some(availableModel =>
                        availableModel.service === savedModel.service &&
                        availableModel.model === savedModel.model
                    );
                });

                // 如果有有效的保存模型，使用它们
                if (validSelectedModels.length > 0) {
                    this.selectedTranslateModels = validSelectedModels;

                    // 设置第一个模型为当前显示模型
                    if (this.selectedTranslateModels.length > 0) {
                        this.currentDisplayModel = this.getModelKey(
                            this.selectedTranslateModels[0].service,
                            this.selectedTranslateModels[0].model
                        );
                    }

                    return true; // 表示成功恢复了保存的选择
                }
            }
        } catch (error) {
            console.error('恢复翻译模型选择异常:', error);
        }

        return false; // 表示没有恢复保存的选择，需要使用默认逻辑
    }

    // 渲染模型结果导航标签
    renderModelTabs() {
        const tabsContainer = document.getElementById('translate-model-tabs');
        if (!tabsContainer) return;

        // 如果没有选中的模型，隐藏标签容器
        if (this.selectedTranslateModels.length === 0) {
            tabsContainer.style.display = 'none';
            return;
        }

        // 显示标签容器
        tabsContainer.style.display = 'flex';

        // 生成模型标签HTML
        const tabsHtml = this.selectedTranslateModels.map(modelConfig => {
            const modelKey = this.getModelKey(modelConfig.service, modelConfig.model);
            const isActive = this.currentDisplayModel === modelKey;

            // 获取模型状态
            const result = this.translateResults[modelKey];
            let statusClass = '';
            if (result) {
                switch (result.status) {
                    case 'pending':
                        statusClass = 'pending';
                        break;
                    case 'streaming':
                        statusClass = 'streaming';
                        break;
                    case 'completed':
                        statusClass = 'completed';
                        break;
                    case 'failed':
                        statusClass = 'failed';
                        break;
                }
            }

            // 获取服务图标
            const iconSvg = this.getServiceIcon(modelConfig.service);
            const iconHtml = iconSvg ? `<div class="model-icon">${iconSvg}</div>` : '';

            // 生成与中间导航栏一致的完整tooltip信息
            let titleText;
            if (modelConfig.type === 'traditional') {
                // 传统翻译服务
                titleText = window.ocrPlugin.configManager.getServiceDisplayName(modelConfig.service);
            } else {
                // AI翻译模型
                let modelDisplayName = modelConfig.model;
                if (modelConfig.service === 'utools') {
                    try {
                        const ocrPlugin = window.ocrPlugin;
                        if (ocrPlugin && ocrPlugin.config && ocrPlugin.config[modelConfig.service]) {
                            const platformConfig = ocrPlugin.config[modelConfig.service];
                            const modelNameMap = platformConfig.modelNameMap || {};
                            modelDisplayName = modelNameMap[modelConfig.model] || modelConfig.model;
                        }
                    } catch (error) {
                        modelDisplayName = modelConfig.model;
                    }
                }
                const serviceName = this.getServiceShortName(modelConfig.service);
                titleText = `${serviceName} - ${modelDisplayName}`;
            }

            return `
                <button type="button"
                        class="translate-model-tab ${isActive ? 'active' : ''}"
                        data-model-key="${modelKey}"
                        title="${titleText}"
                        onclick="window.ocrPlugin.uiManager.switchDisplayModel('${modelKey}')">
                    ${iconHtml}
                    ${statusClass ? `<div class="model-status-indicator ${statusClass}"></div>` : ''}
                </button>
            `;
        }).join('');

        tabsContainer.innerHTML = tabsHtml;

        // 重新设置模型导航的鼠标事件处理
        this.setupModelTabsHoverEvents();
    }

    // 切换显示的模型结果
    switchDisplayModel(modelKey) {
        this.currentDisplayModel = modelKey;

        // 更新结果文本区域
        const resultTextarea = document.getElementById('translate-result-text');
        if (resultTextarea) {
            const result = this.translateResults[modelKey];
            if (result && result.result) {
                resultTextarea.value = result.result;
            } else {
                resultTextarea.value = '';
            }
        }

        // 重新渲染标签以更新active状态
        this.renderModelTabs();
    }

    // 更新翻译模型选择器（当翻译模型列表发生变化时调用）
    updateTranslateModelSelector() {
        // 如果当前在翻译页面，重新渲染选择器
        if (this.currentView === 'translate') {
            this.renderTranslateModelSelector();
        }
    }

    // 确保语言选择器已初始化（避免重复初始化）
    ensureLanguageSelectorsInitialized() {
        // 检查是否已经初始化过
        if (this.translateLanguageOptions && this.languageSelectorsInitialized) {
            // 已初始化，只需要确保样式正确，不重新加载设置以避免覆盖默认值
            this.ensureLanguageSelectorStyles();
            return;
        }

        // 首次初始化
        this.initLanguageSelectors();
    }

    // 确保语言选择器样式正确应用
    ensureLanguageSelectorStyles() {
        // 强制重新应用CSS样式，防止页面切换时样式丢失
        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');

        if (sourceBtn && targetBtn && sourceMenu && targetMenu) {
            // 移除动态设置宽度，让CSS样式控制
            // 确保按钮和菜单的宽度由CSS控制，避免动态修改导致的宽度变化
            setTimeout(() => {
                // 移除任何内联样式，让CSS规则生效
                sourceBtn.style.removeProperty('width');
                targetBtn.style.removeProperty('width');
                sourceMenu.style.removeProperty('width');
                targetMenu.style.removeProperty('width');

                // 强制重新计算布局
                sourceBtn.offsetHeight;
                targetBtn.offsetHeight;
            }, 0);
        }
    }

    // 初始化语言选择器
    initLanguageSelectors() {
        // 设置初始化标志，防止在初始化期间保存设置
        this.isInitializingLanguageSelectors = true;

        // 导入语言配置（需要动态导入）
        import('./language-config.js').then(({ translateLanguageOptions, getLanguageByLangcode, Languages }) => {
            this.translateLanguageOptions = translateLanguageOptions;
            this.getLanguageByLangcode = getLanguageByLangcode;
            this.Languages = Languages;

            // 渲染语言选择器（设置默认值）
            this.renderLanguageSelectors();

            // 绑定语言选择器事件（只绑定一次）
            if (!this.languageSelectorsInitialized) {
                this.bindLanguageSelectorEvents();
                this.languageSelectorsInitialized = true;
            }

            // 加载保存的语言设置
            this.loadLanguageSettings();

            // 初始化完成，清除标志
            this.isInitializingLanguageSelectors = false;
        }).catch(() => {
            console.error('加载语言配置失败');
            this.isInitializingLanguageSelectors = false;
        });
    }

    // 渲染语言选择器
    renderLanguageSelectors() {
        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');

        if (!sourceBtn || !targetBtn || !sourceMenu || !targetMenu || !this.translateLanguageOptions) {
            return;
        }

        // 渲染源语言菜单
        sourceMenu.innerHTML = '';

        // 添加语言选项到源语言菜单（移除自动检测选项，默认选中中文）
        this.translateLanguageOptions.forEach((lang, index) => {
            const option = document.createElement('div');
            option.className = index === 0 ? 'language-option selected' : 'language-option'; // 默认选中中文（简）
            option.setAttribute('data-value', lang.langCode);
            option.innerHTML = `<span>${lang.emoji}</span><span>${lang.label}</span>`;
            sourceMenu.appendChild(option);
        });

        // 渲染目标语言菜单
        targetMenu.innerHTML = '';
        this.translateLanguageOptions.forEach((lang) => {
            const option = document.createElement('div');
            // 默认选中英语作为目标语言（确保选中正确的英语选项）
            const isDefaultTarget = lang.langCode === 'en-us';
            option.className = isDefaultTarget ? 'language-option selected' : 'language-option';
            option.setAttribute('data-value', lang.langCode);
            option.innerHTML = `<span>${lang.emoji}</span><span>${lang.label}</span>`;
            targetMenu.appendChild(option);
        });

        // 检查并设置默认显示文本（只在需要时更新，避免不必要的DOM操作）
        const sourceText = sourceBtn.querySelector('.language-text');
        const targetText = targetBtn.querySelector('.language-text');

        // 检查源语言显示文本是否需要更新
        if (sourceText && !sourceText.querySelector('.language-emoji')) {
            // 只有当HTML中没有结构化格式时才更新
            sourceText.innerHTML = `<span class="language-emoji">${this.translateLanguageOptions[0].emoji}</span><span class="language-label">${this.translateLanguageOptions[0].label}</span>`;
        }

        // 检查目标语言显示文本是否需要更新
        if (targetText && !targetText.querySelector('.language-emoji')) {
            // 只有当HTML中没有结构化格式时才更新
            // 确保默认目标语言是英语，不要回退到中文
            const defaultTargetLang = this.translateLanguageOptions.find(lang => lang.langCode === 'en-us') || this.translateLanguageOptions[1];
            if (defaultTargetLang) {
                targetText.innerHTML = `<span class="language-emoji">${defaultTargetLang.emoji}</span><span class="language-label">${defaultTargetLang.label}</span>`;
            }
        }

        // 确保样式正确应用
        this.ensureLanguageSelectorStyles();

        // 强制确保目标语言正确选中英语（防止任何异步操作导致的选中状态丢失）
        setTimeout(() => {
            const targetBtn = document.getElementById('translate-target-language');
            const targetMenu = document.getElementById('translate-target-menu');
            if (targetBtn && targetMenu) {
                const targetSelected = targetMenu.querySelector('.language-option.selected');
                if (!targetSelected || targetSelected.getAttribute('data-value') !== 'en-us') {
                    // 清除所有选中状态
                    targetMenu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
                    // 选中英语选项
                    const englishOption = targetMenu.querySelector('[data-value="en-us"]');
                    if (englishOption) {
                        englishOption.classList.add('selected');
                        // 更新按钮显示文本
                        const targetText = targetBtn.querySelector('.language-text');
                        if (targetText) {
                            const emojiSpan = englishOption.querySelector('span:first-child');
                            const labelSpan = englishOption.querySelector('span:last-child');
                            if (emojiSpan && labelSpan) {
                                targetText.innerHTML = `<span class="language-emoji">${emojiSpan.textContent}</span><span class="language-label">${labelSpan.textContent}</span>`;
                            }
                        }
                    }
                }
            }
        }, 100);
    }

    // 绑定语言选择器事件
    bindLanguageSelectorEvents() {
        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');

        if (!sourceBtn || !targetBtn || !sourceMenu || !targetMenu) {
            return;
        }

        // 源语言按钮点击事件
        sourceBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleLanguageMenu('source');
        });

        // 目标语言按钮点击事件
        targetBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleLanguageMenu('target');
        });

        // 源语言选项点击事件
        sourceMenu.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                this.selectLanguageOption('source', option);
            }
        });

        // 目标语言选项点击事件
        targetMenu.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                this.selectLanguageOption('target', option);
            }
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            this.closeAllLanguageMenus();
        });

        // 语言交换按钮事件
        const swapBtn = document.getElementById('swap-languages-btn');
        if (swapBtn) {
            swapBtn.addEventListener('click', () => {
                this.swapLanguages();
            });
        }
    }

    // 切换语言菜单显示状态
    toggleLanguageMenu(type) {
        const menu = document.getElementById(`translate-${type}-menu`);
        const otherMenu = document.getElementById(`translate-${type === 'source' ? 'target' : 'source'}-menu`);

        if (!menu) return;

        // 关闭其他菜单
        if (otherMenu) {
            otherMenu.classList.remove('show');
        }

        // 切换当前菜单
        menu.classList.toggle('show');
    }

    // 关闭所有语言菜单
    closeAllLanguageMenus() {
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');

        if (sourceMenu) sourceMenu.classList.remove('show');
        if (targetMenu) targetMenu.classList.remove('show');
    }

    // 选择语言选项
    selectLanguageOption(type, option, skipSave = false) {
        const value = option.getAttribute('data-value');
        const btn = document.getElementById(`translate-${type}-language`);
        const menu = document.getElementById(`translate-${type}-menu`);

        if (!btn || !menu) return;

        // 标记用户手动修改了语言选择，暂停智能检测
        if (!skipSave) {
            this.userManuallyChanged = true;
        }

        // 更新按钮显示文本（使用结构化HTML）
        const textSpan = btn.querySelector('.language-text');
        if (textSpan) {
            const emojiSpan = option.querySelector('span:first-child');
            const labelSpan = option.querySelector('span:last-child');
            if (emojiSpan && labelSpan) {
                textSpan.innerHTML = `<span class="language-emoji">${emojiSpan.textContent}</span><span class="language-label">${labelSpan.textContent}</span>`;
            } else {
                // 兼容旧格式
                textSpan.textContent = option.textContent;
            }
        }

        // 更新选中状态
        menu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        option.classList.add('selected');

        // 关闭菜单
        menu.classList.remove('show');

        // 只有在非初始化状态下才保存设置
        if (!skipSave && !this.isInitializingLanguageSelectors) {
            this.saveLanguageSettings();
        }
    }

    // 获取当前选中的语言
    getCurrentLanguages() {
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');

        let sourceLanguage = null;
        let targetLanguage = null;

        if (sourceMenu) {
            const selectedSource = sourceMenu.querySelector('.language-option.selected');
            if (selectedSource) {
                const langCode = selectedSource.getAttribute('data-value');
                // 从语言配置中找到对应的语言对象
                sourceLanguage = this.translateLanguageOptions.find(lang => lang.langCode === langCode);
            }
        }

        if (targetMenu) {
            const selectedTarget = targetMenu.querySelector('.language-option.selected');
            if (selectedTarget) {
                const langCode = selectedTarget.getAttribute('data-value');
                // 从语言配置中找到对应的语言对象
                targetLanguage = this.translateLanguageOptions.find(lang => lang.langCode === langCode);
            }
        }

        // 如果没有找到源语言，默认使用中文
        if (!sourceLanguage) {
            sourceLanguage = this.translateLanguageOptions.find(lang => lang.langCode === 'zh-cn') || this.translateLanguageOptions[0];
        }

        // 如果没有找到目标语言，默认使用英语
        if (!targetLanguage) {
            // 确保默认目标语言是英语，不要回退到中文
            targetLanguage = this.translateLanguageOptions.find(lang => lang.langCode === 'en-us') || this.translateLanguageOptions[1];
        }

        return { sourceLanguage, targetLanguage };
    }

    // 保存语言设置
    saveLanguageSettings() {
        const { sourceLanguage, targetLanguage } = this.getCurrentLanguages();

        const settings = {
            sourceLanguage: sourceLanguage ? sourceLanguage.langCode : 'zh-cn',
            targetLanguage: targetLanguage ? targetLanguage.langCode : 'en-us'
        };

        // uTools的dbStorage可以直接存储对象，localStorage需要JSON序列化
        const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
        const dataToStore = isUToolsStorage ? settings : JSON.stringify(settings);

        this.setStorageItem('translateLanguageSettings', dataToStore);
    }

    // 加载语言设置
    loadLanguageSettings() {
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');
        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');

        if (!sourceMenu || !targetMenu || !sourceBtn || !targetBtn) {
            return;
        }

        try {
            const stored = this.getStorageItem('translateLanguageSettings');
            let settings = {};

            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                settings = typeof stored === 'string' ? JSON.parse(stored) : stored;
            }

            // 只有在有明确保存的设置时才加载，否则保持默认值
            if (Object.keys(settings).length === 0) {
                return;
            }

            // 检查并清理错误的设置：如果源语言被错误设置为与目标语言相同，重置为默认值
            if (settings.sourceLanguage && settings.sourceLanguage === settings.targetLanguage) {
                settings.sourceLanguage = 'zh-cn';
                settings.targetLanguage = 'en-us';

                // 保存修正后的设置
                const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
                const dataToStore = isUToolsStorage ? settings : JSON.stringify(settings);
                this.setStorageItem('translateLanguageSettings', dataToStore);
            }

            // 检查并清理错误的目标语言设置：如果目标语言被错误设置为中文，重置为英语
            if (settings.targetLanguage && settings.targetLanguage === 'zh-cn') {
                settings.targetLanguage = 'en-us';

                // 保存修正后的设置
                const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
                const dataToStore = isUToolsStorage ? settings : JSON.stringify(settings);
                this.setStorageItem('translateLanguageSettings', dataToStore);
            }

            // 设置源语言（只有在保存了非默认值时才更新）
            if (settings.sourceLanguage && settings.sourceLanguage !== 'zh-cn') {
                const sourceOption = sourceMenu.querySelector(`[data-value="${settings.sourceLanguage}"]`);
                if (sourceOption) {
                    this.selectLanguageOption('source', sourceOption, true); // 跳过保存
                }
            }

            // 设置目标语言（只有在保存了非默认值时才更新）
            if (settings.targetLanguage && settings.targetLanguage !== 'en-us') {
                const targetOption = targetMenu.querySelector(`[data-value="${settings.targetLanguage}"]`);
                if (targetOption) {
                    this.selectLanguageOption('target', targetOption, true); // 跳过保存
                }
            }
        } catch (error) {
            console.error('加载语言设置失败');
        }
    }





    // 绑定翻译按钮事件
    bindTranslateButtonEvents() {
        // 防止重复绑定事件
        if (this.translateButtonEventsInitialized) {
            return;
        }

        const translateBtn = document.getElementById('translate-execute-btn');
        if (translateBtn) {
            translateBtn.addEventListener('click', () => {
                this.executeTranslation();
            });
        }

        // 绑定回车键翻译
        const sourceTextarea = document.getElementById('translate-source-text');
        if (sourceTextarea) {
            sourceTextarea.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.executeTranslation();
                }
            });

            // 绑定智能语言检测
            this.bindSmartLanguageDetection(sourceTextarea);
        }

        // 标记事件已绑定
        this.translateButtonEventsInitialized = true;
    }

    // 绑定智能语言检测
    bindSmartLanguageDetection(sourceTextarea) {
        // 初始化智能检测相关状态
        this.smartDetectionEnabled = true; // 智能检测开关
        this.userManuallyChanged = false; // 用户是否手动修改过语言
        this.detectionTimeout = null; // 防抖定时器
        this.autoTranslateTimeout = null; // 自动翻译防抖定时器

        // 监听输入事件
        sourceTextarea.addEventListener('input', (e) => {
            const inputValue = e.target.value;
            this.handleTextInput(inputValue);

            // 检测输入框是否被清空
            if (!inputValue.trim()) {
                // 输入框被清空，触发清空按钮功能（不显示消息提示，不重复重置智能检测状态）
                this.clearTranslateContent(false, false);
                // 清除自动翻译定时器
                if (this.autoTranslateTimeout) {
                    clearTimeout(this.autoTranslateTimeout);
                    this.autoTranslateTimeout = null;
                }
                // 在这里重置智能检测状态，避免在clearTranslateContent中重复执行
                this.resetSmartDetection();
            }
        });

        // 监听清空事件（作为input事件的补充，处理某些特殊情况）
        sourceTextarea.addEventListener('change', (e) => {
            if (!e.target.value.trim()) {
                // 输入框被清空，确保清空翻译结果（不显示消息提示，不重复重置智能检测状态）
                const resultTextarea = document.getElementById('translate-result-text');
                if (resultTextarea && resultTextarea.value.trim()) {
                    this.clearTranslateContent(false, false);
                }
            }
        });

        // 监听失去焦点事件，触发自动翻译
        sourceTextarea.addEventListener('blur', (e) => {
            const text = e.target.value.trim();
            if (text) {
                // 检查是否启用自动翻译
                const config = window.ocrPlugin?.configManager?.getConfig();
                if (config?.ui?.autoTranslate) {
                    // 延迟一点时间，确保焦点已经完全失去
                    setTimeout(() => {
                        this.performAutoTranslation(text);
                    }, 100);
                }
            }
        });
    }

    // 处理文本输入
    handleTextInput(text) {
        // 如果智能检测被禁用或用户手动修改过，则不进行自动检测
        if (!this.smartDetectionEnabled || this.userManuallyChanged) {
            // 即使智能检测被禁用，仍然可以进行自动翻译
            this.handleAutoTranslate(text);
            return;
        }

        // 清除之前的定时器
        if (this.detectionTimeout) {
            clearTimeout(this.detectionTimeout);
        }

        // 设置防抖延迟
        this.detectionTimeout = setTimeout(() => {
            this.performSmartLanguageDetection(text);
        }, 500); // 500ms防抖

        // 处理自动翻译
        this.handleAutoTranslate(text);
    }

    // 处理自动翻译
    handleAutoTranslate(text) {
        // 检查是否启用自动翻译
        const config = window.ocrPlugin?.configManager?.getConfig();
        if (!config?.ui?.autoTranslate) {
            return;
        }

        // 检查输入框是否有焦点（编辑状态）
        const sourceTextarea = document.getElementById('translate-source-text');
        if (sourceTextarea && document.activeElement === sourceTextarea) {
            // 如果输入框有焦点，不触发自动翻译，避免干扰用户输入
            return;
        }

        // 清除之前的自动翻译定时器
        if (this.autoTranslateTimeout) {
            clearTimeout(this.autoTranslateTimeout);
        }

        // 如果文本为空，不进行翻译
        const trimmedText = text.trim();
        if (!trimmedText) {
            return;
        }

        // 设置自动翻译防抖延迟（1.5秒）
        this.autoTranslateTimeout = setTimeout(() => {
            this.performAutoTranslation(trimmedText);
        }, 1500);
    }

    // 执行自动翻译
    async performAutoTranslation(text) {
        // 如果正在翻译中，跳过
        if (this.isTranslating) {
            return;
        }

        // 检查是否有翻译模型
        const currentModel = window.ocrPlugin?.configManager?.getCurrentTranslateModel();
        if (!currentModel) {
            return; // 静默失败，不显示错误提示
        }

        try {
            // 先执行语言检测和智能切换，然后再翻译
            await this.executeTranslationWithLanguageDetection(text);
        } catch (error) {
            // 自动翻译失败时静默处理，不显示错误提示
        }
    }

    // 执行智能语言检测
    async performSmartLanguageDetection(text) {
        const trimmedText = text.trim();
        if (!trimmedText || trimmedText.length < 2) {
            return; // 文本太短，不进行检测
        }

        try {
            // 动态导入语言检测函数
            import('./language-config.js').then(({ detectLanguageByUnicode }) => {
                const detectedLanguage = detectLanguageByUnicode(trimmedText);
                this.applySmartLanguageSwitch(detectedLanguage);
            }).catch(() => {
                console.warn('语言检测失败');
            });
        } catch (error) {
            console.warn('语言检测失败');
        }
    }

    // 确定智能目标语言（不直接修改UI，只返回目标语言）
    determineSmartTargetLanguage(detectedLanguage) {
        // 根据检测到的语言智能确定目标语言
        if (detectedLanguage.langCode === 'zh-cn') {
            // 检测到中文（简），目标语言为英语
            return this.Languages.enUS;
        } else {
            // 检测到非中文（简）的任何语言，目标语言为中文（简）
            return this.Languages.zhCN;
        }
    }

    // 应用智能语言切换
    applySmartLanguageSwitch(detectedLanguage) {
        if (!detectedLanguage) return;

        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');

        if (!sourceBtn || !targetBtn) return;

        // 获取当前源语言和目标语言
        const currentSourceLangCode = this.getCurrentLanguageCode('source');
        const currentTargetLangCode = this.getCurrentLanguageCode('target');

        // 如果检测到的语言与当前源语言不同，更新源语言
        if (detectedLanguage.langCode !== currentSourceLangCode) {
            this.updateLanguageSelector('source', detectedLanguage, true); // true表示是自动切换
        }

        // 确定新的目标语言
        const newTargetLanguage = this.determineSmartTargetLanguage(detectedLanguage);

        // 如果需要切换目标语言，执行切换
        if (newTargetLanguage.langCode !== currentTargetLangCode) {
            this.updateLanguageSelector('target', newTargetLanguage, true); // true表示是自动切换
        }
    }

    // 获取当前语言代码
    getCurrentLanguageCode(type) {
        const btn = document.getElementById(`translate-${type}-language`);
        if (!btn) return null;

        const menu = document.getElementById(`translate-${type}-menu`);
        if (!menu) return null;

        const selectedOption = menu.querySelector('.language-option.selected');
        return selectedOption ? selectedOption.getAttribute('data-value') : null;
    }

    // 更新语言选择器
    updateLanguageSelector(type, language, isAutoSwitch = false) {
        const btn = document.getElementById(`translate-${type}-language`);
        const menu = document.getElementById(`translate-${type}-menu`);

        if (!btn || !menu) return;

        // 更新选中状态
        menu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
        const targetOption = menu.querySelector(`[data-value="${language.langCode}"]`);

        if (targetOption) {
            targetOption.classList.add('selected');

            // 更新按钮显示
            const textSpan = btn.querySelector('.language-text');
            if (textSpan) {
                const emojiSpan = targetOption.querySelector('span:first-child');
                const labelSpan = targetOption.querySelector('span:last-child');
                if (emojiSpan && labelSpan) {
                    textSpan.innerHTML = `<span class="language-emoji">${emojiSpan.textContent}</span><span class="language-label">${labelSpan.textContent}</span>`;
                } else {
                    textSpan.textContent = targetOption.textContent;
                }
            }

            // 如果不是自动切换，保存设置
            if (!isAutoSwitch) {
                this.saveLanguageSettings();
            }
        }
    }



    // 重置智能检测状态
    resetSmartDetection() {
        this.userManuallyChanged = false;
        this.smartDetectionEnabled = true;

        // 清除防抖定时器
        if (this.detectionTimeout) {
            clearTimeout(this.detectionTimeout);
            this.detectionTimeout = null;
        }

        // 清除自动翻译定时器
        if (this.autoTranslateTimeout) {
            clearTimeout(this.autoTranslateTimeout);
            this.autoTranslateTimeout = null;
        }
    }

    // 执行翻译（保持原有逻辑，用于手动翻译）
    async executeTranslation() {
        const sourceTextarea = document.getElementById('translate-source-text');
        const text = sourceTextarea?.value?.trim();

        if (!text) {
            this.showNotification('请输入要翻译的文本', 'warning');
            return;
        }

        // 对于手动翻译，直接使用当前UI状态的语言设置
        await this.executeTranslationWithLanguageDetection(text, false);
    }

    // 执行翻译并集成语言检测（新的核心翻译方法）
    async executeTranslationWithLanguageDetection(text, enableSmartSwitch = true) {
        // 防止重复执行翻译
        if (this.isTranslating) {
            return;
        }

        const sourceTextarea = document.getElementById('translate-source-text');
        const resultTextarea = document.getElementById('translate-result-text');
        const translateBtn = document.getElementById('translate-execute-btn');

        if (!sourceTextarea || !resultTextarea || !translateBtn) {
            return;
        }

        if (!text) {
            this.showNotification('请输入要翻译的文本', 'warning');
            return;
        }

        // 检查是否有选中的翻译模型
        if (this.selectedTranslateModels.length === 0) {
            this.showNotification('请先选择翻译模型', 'error');
            return;
        }

        // 获取当前选择的语言
        const languages = this.getCurrentLanguages();

        if (!languages.targetLanguage) {
            this.showNotification('请选择目标语言', 'error');
            return;
        }

        // 1. 始终进行智能语言检测
        let sourceLanguage = languages.sourceLanguage;
        let detectedLanguage = null;

        try {
            // 动态导入语言检测功能
            const { detectLanguage } = await import('./language-config.js');
            detectedLanguage = await detectLanguage(text);

            // 如果启用智能切换且用户未手动修改过语言，使用检测到的语言作为源语言
            if (enableSmartSwitch && this.smartDetectionEnabled && !this.userManuallyChanged) {
                sourceLanguage = detectedLanguage;
            }
        } catch (error) {
            console.error('语言检测失败');
            // 检测失败时使用用户选择的源语言
        }

        // 2. 如果启用智能切换，应用智能语言切换逻辑
        let finalTargetLanguage = languages.targetLanguage;
        if (enableSmartSwitch && detectedLanguage && this.smartDetectionEnabled && !this.userManuallyChanged) {
            finalTargetLanguage = this.determineSmartTargetLanguage(detectedLanguage);

            // 如果目标语言发生了变化，更新UI
            if (finalTargetLanguage.langCode !== languages.targetLanguage.langCode) {
                this.updateLanguageSelector('target', finalTargetLanguage, true);

                // 等待UI更新完成
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }

        // 3. 检查源语言和目标语言是否相同
        if (sourceLanguage.langCode === finalTargetLanguage.langCode) {
            this.showNotification('源语言和目标语言不能相同', 'warning');
            return;
        }

        // 4. 设置翻译状态
        this.isTranslating = true;
        this.setTranslationLoading(true);
        resultTextarea.value = '';

        // 设置翻译中状态的占位符
        this.setTranslatingPlaceholder();

        try {
            // 获取配置
            const config = window.ocrPlugin?.configManager?.getConfig();
            if (!config) {
                throw new Error('无法获取配置信息');
            }

            // 清空所有模型的翻译结果
            this.translateResults = {};

            // 立即设置第一个模型为当前显示模型
            if (this.selectedTranslateModels.length > 0) {
                const firstModelKey = this.getModelKey(
                    this.selectedTranslateModels[0].service,
                    this.selectedTranslateModels[0].model
                );
                this.currentDisplayModel = firstModelKey;
                this.renderModelTabs(); // 更新模型导航显示
            }

            // 为每个选中的模型创建翻译任务
            const translationTasks = this.selectedTranslateModels.map(async (modelConfig) => {
                const modelKey = this.getModelKey(modelConfig.service, modelConfig.model);

                // 初始化结果状态
                this.translateResults[modelKey] = {
                    result: '',
                    status: 'pending',
                    error: null
                };

                try {
                    let result;

                    if (modelConfig.type === 'traditional') {
                        // 使用传统翻译API
                        const translateConfig = window.ocrPlugin.configManager.getTraditionalTranslateConfig(modelConfig.service);
                        if (!translateConfig) {
                            throw new Error(`${modelConfig.service}翻译API未配置`);
                        }

                        // 转换语言代码为传统API支持的格式
                        const sourceCode = this.convertLanguageCodeForTraditional(sourceLanguage.langCode, modelConfig.service);
                        const targetCode = this.convertLanguageCodeForTraditional(finalTargetLanguage.langCode, modelConfig.service);

                        this.translateResults[modelKey].status = 'streaming';
                        this.renderModelTabs(); // 更新状态显示

                        result = await window.ocrPlugin.ocrServices.performTraditionalTranslation(
                            text,
                            sourceCode,
                            targetCode,
                            modelConfig.service,
                            translateConfig
                        );
                    } else {
                        // 使用AI模型翻译
                        this.translateResults[modelKey].status = 'streaming';
                        this.renderModelTabs(); // 更新状态显示

                        result = await window.ocrPlugin.ocrServices.performTranslation(
                            text,
                            modelConfig.service,
                            modelConfig.model,
                            config,
                            (chunk, fullText) => {
                                // 流式输出回调
                                const currentResult = fullText !== undefined ? fullText : (this.translateResults[modelKey].result + chunk);
                                this.translateResults[modelKey].result = currentResult;

                                // 如果当前显示的是这个模型，更新UI
                                if (this.currentDisplayModel === modelKey) {
                                    resultTextarea.value = currentResult;
                                    resultTextarea.scrollTop = resultTextarea.scrollHeight;
                                }
                            },
                            finalTargetLanguage,
                            sourceLanguage,
                            null
                        );
                    }

                    if (result.success) {
                        this.translateResults[modelKey].result = result.translatedText || result.text || result.fullText || '';
                        this.translateResults[modelKey].status = 'completed';
                    } else {
                        this.translateResults[modelKey].error = result.error || '翻译失败';
                        this.translateResults[modelKey].status = 'failed';
                    }
                } catch (error) {
                    this.translateResults[modelKey].error = error.message || '翻译失败';
                    this.translateResults[modelKey].status = 'failed';
                }

                // 更新状态显示
                this.renderModelTabs();

                // 如果当前显示的是这个模型，更新结果显示
                if (this.currentDisplayModel === modelKey) {
                    this.switchDisplayModel(modelKey);
                }

                return { modelKey, result: this.translateResults[modelKey] };
            });

            // 等待所有翻译任务完成
            const results = await Promise.allSettled(translationTasks);

            // 检查是否有成功的翻译
            const successfulResults = results.filter(r => r.status === 'fulfilled' && r.value.result.status === 'completed');

            if (successfulResults.length > 0) {
                // 只有在当前显示模型翻译失败时，才切换到第一个成功的模型
                if (this.currentDisplayModel && this.translateResults[this.currentDisplayModel] && this.translateResults[this.currentDisplayModel].status === 'failed') {
                    this.currentDisplayModel = successfulResults[0].value.modelKey;
                    this.switchDisplayModel(this.currentDisplayModel);
                }
                // 如果当前显示模型为空（不应该发生，但作为保险），设置为第一个成功的模型
                else if (!this.currentDisplayModel) {
                    this.currentDisplayModel = successfulResults[0].value.modelKey;
                    this.switchDisplayModel(this.currentDisplayModel);
                }
            }

            // 更新翻译使用统计
            if (typeof this.incrementUsageStats === 'function') {
                this.incrementUsageStats('translate');
            }

            // 延迟保存翻译历史记录，确保流式输出完成
            setTimeout(() => {
                if (successfulResults.length > 0) {
                    const currentResult = this.translateResults[this.currentDisplayModel];
                    if (currentResult && currentResult.result && currentResult.result.trim()) {
                        this.saveTranslateHistory(text, currentResult.result.trim(), sourceLanguage, finalTargetLanguage);
                    }
                }
            }, 1000);

            // 翻译成功后清除状态类，恢复默认占位符
            if (!resultTextarea.value.trim()) {
                this.restoreTranslateDefaultPlaceholder();
            } else {
                // 有翻译结果时，只清除状态类，不改变占位符
                const translateResultText = document.getElementById('translate-result-text');
                if (translateResultText) {
                    translateResultText.classList.remove('translating', 'error');
                }
            }

            // 显示翻译完成通知
            const completedCount = successfulResults.length;
            const totalCount = this.selectedTranslateModels.length;
            if (completedCount === totalCount) {
                this.showNotification('所有模型翻译完成', 'success');
            } else if (completedCount > 0) {
                this.showNotification(`${completedCount}/${totalCount} 个模型翻译完成`, 'success');
            } else {
                this.showNotification('翻译失败', 'error');
            }

            // 自动复制翻译结果（如果配置了）
            if (config?.ui?.autoCopyAfterTranslate && successfulResults.length > 0) {
                const currentResult = this.translateResults[this.currentDisplayModel];
                if (currentResult && currentResult.result && currentResult.result.trim()) {
                    window.ocrAPI?.copyText?.(currentResult.result.trim());
                    this.showSuccess('已自动复制到剪贴板');
                }
            }
        } catch (error) {
            console.error('翻译失败:', error);
            this.showNotification('翻译失败: ' + error.message, 'error');
            resultTextarea.value = '';

            // 设置翻译失败状态的占位符
            this.setTranslateErrorPlaceholder('翻译失败，请重试');
        } finally {
            this.isTranslating = false;
            this.setTranslationLoading(false);
        }
    }

    // 设置翻译加载状态
    setTranslationLoading(loading) {
        const translateBtn = document.getElementById('translate-execute-btn');
        const sourceTextarea = document.getElementById('translate-source-text');

        if (translateBtn) {
            translateBtn.disabled = loading;
            const btnText = translateBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.textContent = loading ? '翻译中...' : '翻译';
            }
        }

        if (sourceTextarea) {
            sourceTextarea.disabled = loading;
        }
    }

    // 绑定翻译页面其他事件
    bindTranslatePageEvents() {
        // 防止重复绑定事件
        if (this.translatePageEventsInitialized) {
            return;
        }

        // 绑定复制翻译结果按钮
        const copyBtn = document.getElementById('translate-copy-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                this.copyTranslateResult();
            });
        }

        // 绑定清空按钮
        const clearBtn = document.getElementById('translate-clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearTranslateContent();
            });
        }

        // 绑定返回按钮
        const backBtn = document.getElementById('translate-back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.showMainView();
            });
        }

        // 绑定设置按钮
        const settingsBtn = document.getElementById('translate-settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showConfigView();
            });
        }

        // 绑定历史记录按钮
        const historyBtn = document.getElementById('translate-history-btn');
        if (historyBtn) {
            historyBtn.addEventListener('click', () => {
                this.showHistoryView();
            });
        }

        // 标记事件已绑定
        this.translatePageEventsInitialized = true;
    }

    // 绑定图片翻译页面事件
    bindImageTranslatePageEvents() {
        // 防止重复绑定事件
        if (this.imageTranslatePageEventsInitialized) {
            return;
        }

        // 绑定复制翻译结果按钮
        const copyBtn = document.getElementById('image-translate-copy-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                this.copyImageTranslateResult();
            });
        }

        // 绑定清空按钮
        const clearBtn = document.getElementById('image-translate-clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearImageTranslateContent();
            });
        }

        // 这个按钮的事件绑定已经在下面处理了

        // 绑定设置按钮
        const settingsBtn = document.getElementById('image-translate-settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showConfigView();
            });
        }

        // 绑定返回翻译页面按钮
        const toTranslateBtn = document.getElementById('image-translate-to-translate-btn');
        if (toTranslateBtn) {
            toTranslateBtn.addEventListener('click', () => {
                this.showTranslateView();
            });
        }

        // 绑定翻译执行按钮
        const executeBtn = document.getElementById('image-translate-execute-btn');
        if (executeBtn) {
            executeBtn.addEventListener('click', () => {
                this.executeImageTranslation();
            });
        }

        // 绑定截图按钮
        const screenshotBtn = document.getElementById('image-translate-screenshot-btn');
        if (screenshotBtn) {
            screenshotBtn.addEventListener('click', () => {
                this.takeImageTranslateScreenshot();
            });
        }

        // 绑定上传按钮
        const uploadBtn = document.getElementById('image-translate-upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.triggerImageTranslateFileSelect();
            });
        }

        // 绑定底部重新翻译按钮
        const retestBtnBottom = document.getElementById('image-translate-retest-btn-bottom');
        if (retestBtnBottom) {
            retestBtnBottom.addEventListener('click', () => {
                this.executeImageTranslation();
            });
        }

        // 绑定返回历史记录页面按钮
        const toHistoryBtn = document.getElementById('image-translate-to-history-btn');
        if (toHistoryBtn) {
            toHistoryBtn.addEventListener('click', () => {
                this.showHistoryView();
            });
        }



        // 绑定返回OCR页面按钮
        const imageBackBtn = document.getElementById('image-translate-back-btn');
        if (imageBackBtn) {
            imageBackBtn.addEventListener('click', () => {
                this.showMainView();
            });
        }

        // 绑定文件输入框
        const fileInput = document.getElementById('image-translate-file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (event) => {
                this.handleImageTranslateFileSelect(event);
            });
        }

        // 绑定拖拽事件
        this.bindImageTranslateDragEvents();

        // 初始化图片翻译页面的语言选择器
        this.initImageTranslateLanguageSelectors();

        // 初始化贴合模式选择器
        this.initImageTranslatePasteModeSelector();

        // 设置图片翻译按钮控制
        this.setupImageTranslateButtonsControl();



        this.imageTranslatePageEventsInitialized = true;
    }

    // 绑定图片翻译按钮事件
    bindImageTranslateButtonEvent() {
        // 防止重复绑定事件
        if (this.imageTranslateButtonEventInitialized) {
            return;
        }

        const imageTranslateBtn = document.getElementById('image-language-btn');
        if (imageTranslateBtn) {
            imageTranslateBtn.addEventListener('click', () => {
                this.showImageTranslateView();
            });
        }

        this.imageTranslateButtonEventInitialized = true;
    }

    // 聚焦图片翻译输入区域（现在是图片输入区域）
    focusImageTranslateInput() {
        // 图片翻译页面不需要聚焦特定元素，因为是图片输入
        // 可以在这里添加一些初始化逻辑，比如检查是否有剪贴板图片等
    }



    // 执行图片翻译
    async executeImageTranslation() {
        // 检查是否有上传的图片
        if (!this.currentImageTranslateBase64) {
            this.showNotification('请先上传或截取要翻译的图片', 'warning');
            return;
        }

        try {
            // 显示翻译进行中状态（在输出区域）
            this.showImageTranslateProcessing('正在翻译...', '请稍候，正在处理您的图片');

            // 显示全局加载状态
            this.showLoading('正在翻译图片...');

            // 获取翻译配置
            const config = window.ocrPlugin.config;
            const translateConfig = window.ocrPlugin.configManager.getTraditionalTranslateConfig('baidu');

            if (!translateConfig) {
                throw new Error('请先配置百度翻译API密钥');
            }

            // 获取当前选择的语言设置和贴合模式
            const sourceLanguage = this.currentImageTranslateSourceLang || 'auto';
            const targetLanguage = this.currentImageTranslateTargetLang || 'zh';
            const pasteMode = this.currentImageTranslatePasteMode !== undefined ? this.currentImageTranslatePasteMode : 1;



            // 调用图片翻译API（使用选择的贴合模式）
            const result = await window.ocrPlugin.ocrServices.baiduImageTranslate(
                this.currentImageTranslateBase64,
                sourceLanguage,
                targetLanguage,
                translateConfig,
                pasteMode
            );

            // 隐藏加载状态
            this.hideLoading();
            this.hideImageTranslateProcessing();

            if (result.success) {
                // 显示翻译结果
                this.showImageTranslateResult(result.data);
                this.showNotification('翻译成功', 'success');
            } else {
                console.error('图片翻译失败:', result.error);
                throw new Error(result.error);
            }

        } catch (error) {
            // 隐藏所有加载状态
            this.hideLoading();
            this.hideImageTranslateProcessing();

            // 恢复占位符显示
            const resultPlaceholder = document.getElementById('image-translate-result-placeholder');
            if (resultPlaceholder) {
                resultPlaceholder.style.display = 'flex';
            }

            console.error('图片翻译失败:', error);
            this.showNotification(`翻译失败: ${error.message}`, 'error');
        }
    }

    // 截图翻译
    takeImageTranslateScreenshot(quickMode = true) {
        try {
            // 如果不是快速模式，显示加载状态
            if (!quickMode) {
                this.showLoading('正在截图...<br><small>按ESC键可取消截图</small>');
            }

            // 截图选项
            const screenshotOptions = {
                quickMode: quickMode,
                keepHidden: false // 图片翻译完成后需要显示窗口
            };

            window.ocrAPI.screenCapture((imageBase64) => {
                if (!quickMode) {
                    this.hideLoading();
                }

                if (imageBase64) {
                    this.setImageTranslateImage(imageBase64);
                    // 截图成功后不显示提示，直接设置图片
                } else {
                    this.showNotification('截图已取消', 'info');
                }
            }, screenshotOptions);
        } catch (error) {
            if (!quickMode) {
                this.hideLoading();
            }
            console.error('截图失败:', error);
            this.showNotification('截图功能启动失败', 'error');
        }
    }

    // 触发文件选择
    triggerImageTranslateFileSelect() {
        const fileInput = document.getElementById('image-translate-file-input');
        if (fileInput) {
            fileInput.click();
        }
    }

    // 处理文件选择
    async handleImageTranslateFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 重置文件输入框
        event.target.value = '';

        if (!file.type.startsWith('image/')) {
            this.showNotification('请选择图片文件', 'error');
            return;
        }

        // 检查文件大小（4MB限制）
        if (file.size > 4 * 1024 * 1024) {
            this.showNotification('图片大小不能超过4MB', 'error');
            return;
        }

        try {
            this.showLoading('正在读取图片...');

            const imageBase64 = await this.readFileAsDataURL(file);
            this.setImageTranslateImage(imageBase64);

            // 根据自动翻译配置显示不同的提示信息
            const config = this.getOcrPlugin()?.configManager?.getConfig();
            if (config?.ui?.autoTranslateImageTranslate !== false) {
                this.showNotification('图片上传成功，正在自动翻译...', 'success');
            } else {
                this.showNotification('图片上传成功，点击翻译按钮开始翻译', 'success');
            }

            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            console.error('读取图片失败:', error);
            this.showNotification('读取图片失败', 'error');
        }
    }

    // 读取文件为DataURL
    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    // 设置图片翻译的图片
    setImageTranslateImage(imageBase64) {
        this.currentImageTranslateBase64 = imageBase64;

        // 显示图片预览
        const preview = document.getElementById('image-translate-preview');
        const previewImg = document.getElementById('image-translate-preview-img');
        const placeholder = document.getElementById('image-translate-placeholder');

        if (preview && previewImg && placeholder) {
            previewImg.src = imageBase64;
            preview.style.display = 'flex';
            placeholder.style.display = 'none';

            // 初始化输入图片的缩放功能
            this.initImageTranslateInputZoom();
        }

        // 清空之前的翻译结果
        this.clearImageTranslateResult();

        // 显示翻译按钮
        this.showImageTranslateButtons();

        // 检查是否启用自动翻译
        const config = this.getOcrPlugin()?.configManager?.getConfig();
        if (config?.ui?.autoTranslateImageTranslate !== false) { // 默认开启
            // 延迟一点时间，确保UI更新完成
            setTimeout(() => {
                this.executeImageTranslation();
            }, 100);
        }
    }

    // 显示图片翻译结果
    showImageTranslateResult(data) {
        // 保存翻译结果数据
        this.currentImageTranslateResult = data;

        // 根据当前贴合模式显示相应的结果
        this.renderImageTranslateResult(data, this.currentImageTranslatePasteMode);

        // 显示操作按钮
        this.showImageTranslateButtons();



        // 保存图片翻译历史记录
        this.saveImageTranslateHistory(data);
    }

    // 保存图片翻译历史记录
    saveImageTranslateHistory(data) {
        try {
            // 检查是否有历史记录管理器
            const historyManager = window.ocrPlugin?.historyManager;
            if (!historyManager) {
                console.warn('历史记录管理器未初始化，无法保存图片翻译记录');
                return;
            }

            // 获取语言信息
            const sourceLanguage = this.currentImageTranslateSourceLang || 'auto';
            const targetLanguage = this.currentImageTranslateTargetLang || 'zh';
            const pasteMode = this.currentImageTranslatePasteMode !== undefined ? this.currentImageTranslatePasteMode : 1;

            // 调用历史记录管理器保存图片翻译记录
            historyManager.addImageTranslateHistory(
                this.currentImageTranslateBase64, // 原始图片
                data, // 翻译结果数据
                sourceLanguage,
                targetLanguage,
                pasteMode,
                'baidu' // 服务商
            );
        } catch (error) {
            console.error('保存图片翻译历史记录失败:', error);
        }
    }

    // 根据模式渲染翻译结果
    renderImageTranslateResult(data, mode) {
        const resultPreview = document.getElementById('image-translate-result-preview');
        const resultImg = document.getElementById('image-translate-result-img');
        const textResult = document.getElementById('image-translate-text-result');
        const resultPlaceholder = document.getElementById('image-translate-result-placeholder');

        if (!resultPreview || !resultImg || !textResult || !resultPlaceholder) return;

        // 隐藏占位符
        resultPlaceholder.style.display = 'none';

        if (mode === 0) {
            // 文本结果模式
            this.showTextResult(data);
            resultPreview.style.display = 'none';
            textResult.style.display = 'flex';
        } else if (mode === 1) {
            // 图片结果模式
            if (data.translatedImage) {
                resultImg.src = data.translatedImage;
                resultPreview.style.display = 'flex';
                textResult.style.display = 'none';

                // 初始化翻译结果图片的缩放功能
                this.initImageTranslateResultZoom();
            } else {
                // 如果没有翻译图片，显示提示信息
                this.showTextResult({ sumDst: '图片结果模式：未生成翻译图片\n\n' + (data.sumDst || '翻译结果为空') });
                resultPreview.style.display = 'none';
                textResult.style.display = 'flex';
            }
        } else {
            // 默认显示文本结果
            this.showTextResult(data);
            resultPreview.style.display = 'none';
            textResult.style.display = 'flex';
        }
    }

    // 显示文本翻译结果
    showTextResult(data) {
        const resultTextArea = document.getElementById('image-translate-result-text');

        if (!resultTextArea) return;

        // 显示完整译文
        resultTextArea.value = data.sumDst || '翻译结果为空';
    }

    // 处理模式切换
    async handleModeSwitch(newMode) {
        const currentData = this.currentImageTranslateResult;

        // 如果切换到图片结果模式，但当前数据没有翻译图片，需要重新翻译
        if (newMode === 1 && (!currentData.translatedImage || currentData.pasteType === 0)) {
            console.log('切换到图片结果模式，需要重新翻译以获取翻译图片');

            // 显示翻译进行中状态
            this.showImageTranslateProcessing('正在获取翻译图片...', '正在重新生成图片结果');
            this.showImageTranslateButtons(false);
            this.showNotification('正在获取翻译图片...', 'info');

            try {
                // 重新调用翻译API，使用整图贴合模式
                const sourceLanguage = this.currentImageTranslateSourceLang || 'auto';
                const targetLanguage = this.currentImageTranslateTargetLang || 'zh';

                // 获取翻译配置
                const ocrPlugin = this.getOcrPlugin();
                const translateConfig = ocrPlugin?.configManager?.getTraditionalTranslateConfig('baidu');

                const result = await ocrPlugin.ocrServices.baiduImageTranslate(
                    this.currentImageTranslateBase64,
                    sourceLanguage,
                    targetLanguage,
                    translateConfig,
                    1 // 强制使用图片结果模式
                );

                if (result.success) {
                    // 隐藏翻译进行中状态
                    this.hideImageTranslateProcessing();

                    // 更新保存的翻译结果
                    this.currentImageTranslateResult = result.data;

                    // 显示翻译结果
                    this.showImageTranslateResult(result.data);
                    this.renderImageTranslateResult(result.data, newMode);

                    console.log('重新翻译成功，已获取翻译图片');
                    this.showNotification('已切换到图片结果模式', 'success');
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('重新翻译失败:', error);

                // 隐藏翻译进行中状态并恢复占位符
                this.hideImageTranslateProcessing();
                const resultPlaceholder = document.getElementById('image-translate-result-placeholder');
                if (resultPlaceholder) {
                    resultPlaceholder.style.display = 'flex';
                }

                this.showNotification('切换模式失败: ' + error.message, 'error');

                // 回退到文本模式
                this.renderImageTranslateResult(currentData, 0);
            } finally {
                this.showImageTranslateButtons(true);
            }
        } else {
            // 直接切换显示模式（不需要重新翻译）
            this.renderImageTranslateResult(currentData, newMode);

            const modeNames = { 0: '文本结果', 1: '图片结果' };
            this.showNotification(`已切换到${modeNames[newMode]}模式`, 'success');
        }
    }






    // 清空图片翻译结果
    clearImageTranslateResult() {
        const resultPreview = document.getElementById('image-translate-result-preview');
        const textResult = document.getElementById('image-translate-text-result');
        const resultPlaceholder = document.getElementById('image-translate-result-placeholder');
        const processingIndicator = document.getElementById('image-translate-processing');

        if (resultPreview) {
            resultPreview.style.display = 'none';
        }

        if (textResult) {
            textResult.style.display = 'none';
        }

        if (processingIndicator) {
            processingIndicator.style.display = 'none';
        }

        if (resultPlaceholder) {
            resultPlaceholder.style.display = 'flex';
        }

        // 销毁翻译结果图片的缩放管理器
        if (this.imageTranslateResultZoomManager) {
            this.imageTranslateResultZoomManager.destroy();
            this.imageTranslateResultZoomManager = null;
        }

        this.currentImageTranslateResult = null;

        // 隐藏操作按钮（当没有结果时）
        if (!this.currentImageTranslateBase64) {
            this.hideImageTranslateButtons();
        }
    }

    // 显示图片翻译进行中状态
    showImageTranslateProcessing(message = '正在翻译...', description = '请稍候，正在处理您的图片') {
        const resultPreview = document.getElementById('image-translate-result-preview');
        const textResult = document.getElementById('image-translate-text-result');
        const resultPlaceholder = document.getElementById('image-translate-result-placeholder');
        const processingIndicator = document.getElementById('image-translate-processing');

        // 隐藏其他显示区域
        if (resultPreview) {
            resultPreview.style.display = 'none';
        }
        if (textResult) {
            textResult.style.display = 'none';
        }
        if (resultPlaceholder) {
            resultPlaceholder.style.display = 'none';
        }

        // 显示进行中状态
        if (processingIndicator) {
            const titleElement = processingIndicator.querySelector('.processing-title');
            const descElement = processingIndicator.querySelector('.processing-desc');

            if (titleElement) {
                titleElement.textContent = message;
            }
            if (descElement) {
                descElement.textContent = description;
            }

            processingIndicator.style.display = 'flex';
        }
    }

    // 隐藏图片翻译进行中状态
    hideImageTranslateProcessing() {
        const processingIndicator = document.getElementById('image-translate-processing');
        if (processingIndicator) {
            processingIndicator.style.display = 'none';
        }
    }

    // 绑定图片翻译拖拽事件
    bindImageTranslateDragEvents() {
        const inputArea = document.querySelector('.image-translate-input-area');
        if (!inputArea) return;

        // 防止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            inputArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // 拖拽进入和悬停
        ['dragenter', 'dragover'].forEach(eventName => {
            inputArea.addEventListener(eventName, () => {
                inputArea.classList.add('dragover');
            });
        });

        // 拖拽离开
        inputArea.addEventListener('dragleave', () => {
            inputArea.classList.remove('dragover');
        });

        // 文件放置
        inputArea.addEventListener('drop', (e) => {
            inputArea.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            const imageFiles = files.filter(file => file.type.startsWith('image/'));

            if (imageFiles.length === 0) {
                this.showNotification('请拖拽图片文件', 'error');
                return;
            }

            // 处理第一个图片文件
            const file = imageFiles[0];

            // 检查文件大小
            if (file.size > 4 * 1024 * 1024) {
                this.showNotification('图片大小不能超过4MB', 'error');
                return;
            }

            // 读取并设置图片
            this.readFileAsDataURL(file).then(imageBase64 => {
                this.setImageTranslateImage(imageBase64);
                this.showNotification('图片上传成功，点击翻译按钮开始翻译', 'success');
            }).catch(error => {
                console.error('读取图片失败:', error);
                this.showNotification('读取图片失败', 'error');
            });
        });
    }

    // 复制图片翻译结果（智能复制：根据当前模式选择复制内容）
    async copyImageTranslateResult() {
        if (!this.currentImageTranslateResult) {
            this.showNotification('没有可复制的翻译结果', 'warning');
            return;
        }

        const currentMode = this.currentImageTranslatePasteMode;

        if (currentMode === 0) {
            // 文本结果模式：复制翻译文本
            await this.copyImageTranslateText();
        } else if (currentMode === 1) {
            // 图片结果模式：复制翻译图片
            await this.copyImageTranslateImage();
        } else {
            // 默认复制文本
            await this.copyImageTranslateText();
        }
    }

    // 复制图片翻译的文本结果
    async copyImageTranslateText() {
        if (!this.currentImageTranslateResult || !this.currentImageTranslateResult.sumDst) {
            this.showNotification('没有可复制的翻译文本', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(this.currentImageTranslateResult.sumDst);
            this.showNotification('翻译文本已复制到剪贴板', 'success');

            // 检查是否启用复制后自动关闭插件
            const config = this.getOcrPlugin()?.configManager?.getConfig();
            const shouldAutoClose = config?.ui?.autoCloseImageTranslate !== undefined ?
                config.ui.autoCloseImageTranslate === true :
                config?.ui?.autoClose === true;
            if (shouldAutoClose) {
                setTimeout(() => {
                    if (window.utools) {
                        window.utools.hideMainWindow();
                    }
                }, 300);
            }
        } catch (error) {
            console.error('复制文本失败:', error);
            this.showNotification('复制失败，请手动复制', 'error');
        }
    }

    // 复制图片翻译的图片结果
    async copyImageTranslateImage() {
        if (!this.currentImageTranslateResult || !this.currentImageTranslateResult.translatedImage) {
            this.showNotification('没有可复制的翻译图片', 'warning');
            return;
        }

        try {
            // 将base64图片转换为PNG格式的Blob（确保剪贴板兼容性）
            const base64Data = this.currentImageTranslateResult.translatedImage;
            const blob = await this.base64ToBlob(base64Data, true);

            // 检查浏览器是否支持剪贴板API
            if (!navigator.clipboard || !navigator.clipboard.write) {
                throw new Error('浏览器不支持剪贴板API');
            }

            // 使用PNG格式复制到剪贴板
            const clipboardItem = new ClipboardItem({
                'image/png': blob
            });

            await navigator.clipboard.write([clipboardItem]);
            this.showNotification('翻译图片已复制到剪贴板', 'success');

            // 检查是否启用复制后自动关闭插件
            const config = this.getOcrPlugin()?.configManager?.getConfig();
            const shouldAutoClose = config?.ui?.autoCloseImageTranslate !== undefined ?
                config.ui.autoCloseImageTranslate === true :
                config?.ui?.autoClose === true;
            if (shouldAutoClose) {
                setTimeout(() => {
                    if (window.utools) {
                        window.utools.hideMainWindow();
                    }
                }, 300);
            }
        } catch (error) {
            console.error('复制图片失败:', error);

            // 尝试备用复制方法
            try {
                const base64Data = this.currentImageTranslateResult.translatedImage;
                const pngBlob = await this.convertImageToPNG(base64Data);
                await navigator.clipboard.write([
                    new ClipboardItem({
                        'image/png': pngBlob
                    })
                ]);
                this.showNotification('翻译图片已复制到剪贴板', 'success');
                return;
            } catch (backupError) {
                console.error('备用复制方法失败:', backupError);
            }

            // 降级方案：复制文本内容
            if (this.currentImageTranslateResult.sumDst) {
                try {
                    await navigator.clipboard.writeText(this.currentImageTranslateResult.sumDst);
                    this.showNotification('图片复制失败，已复制翻译文本', 'warning');
                } catch (textError) {
                    console.error('文本复制失败:', textError);
                    this.showNotification('复制失败，请手动复制', 'error');
                }
            } else {
                this.showNotification('复制失败，请手动复制', 'error');
            }
        }
    }

    // 将base64字符串转换为Blob对象
    async base64ToBlob(base64Data, forcePNG = false) {
        if (forcePNG) {
            return await this.convertImageToPNG(base64Data);
        }

        // 如果base64数据包含data URL前缀，直接使用fetch
        if (base64Data.startsWith('data:')) {
            const response = await fetch(base64Data);
            return await response.blob();
        }

        // 如果是纯base64数据，添加data URL前缀
        const mimeType = 'image/jpeg';
        const dataUrl = `data:${mimeType};base64,${base64Data}`;
        const response = await fetch(dataUrl);
        return await response.blob();
    }

    // 将图片转换为PNG格式（用于剪贴板兼容性）
    async convertImageToPNG(base64Data) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('PNG转换失败'));
                        }
                    }, 'image/png');
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => {
                reject(new Error('图片加载失败'));
            };

            img.src = base64Data.startsWith('data:') ? base64Data : `data:image/jpeg;base64,${base64Data}`;
        });
    }

    // 清空图片翻译内容（更新）
    clearImageTranslateContent() {
        // 清空当前图片
        this.currentImageTranslateBase64 = null;

        // 隐藏图片预览，显示占位符
        const preview = document.getElementById('image-translate-preview');
        const placeholder = document.getElementById('image-translate-placeholder');

        if (preview && placeholder) {
            preview.style.display = 'none';
            placeholder.style.display = 'flex';
        }

        // 销毁输入图片的缩放管理器
        if (this.imageTranslateInputZoomManager) {
            this.imageTranslateInputZoomManager.destroy();
            this.imageTranslateInputZoomManager = null;
        }

        // 清空翻译结果
        this.clearImageTranslateResult();

        // 隐藏操作按钮
        this.hideImageTranslateButtons();

        this.showNotification('内容已清空', 'success');
    }

    // 初始化图片翻译页面的语言选择器
    initImageTranslateLanguageSelectors() {
        // 导入图片翻译专用语言配置
        import('./language-config.js').then(({ imageTranslateLanguageOptions, getImageTranslateLanguageByLangcode, ImageTranslateLanguages }) => {
            // 初始化源语言选择器（包含自动检测）
            this.initImageTranslateLanguageSelector(
                'image-translate-source-language',
                'image-translate-source-menu',
                imageTranslateLanguageOptions, // 包含auto选项
                'auto',
                (langcode) => {
                    // 源语言变化回调
                    // 存储当前选择的源语言
                    this.currentImageTranslateSourceLang = langcode;
                }
            );

            // 初始化目标语言选择器（不包含自动检测）
            this.initImageTranslateLanguageSelector(
                'image-translate-target-language',
                'image-translate-target-menu',
                imageTranslateLanguageOptions.filter(lang => lang.langcode !== 'auto'), // 目标语言不包含自动检测
                'zh',
                (langcode) => {
                    // 目标语言变化回调
                    // 存储当前选择的目标语言
                    this.currentImageTranslateTargetLang = langcode;
                }
            );

            // 初始化默认语言
            this.currentImageTranslateSourceLang = 'auto';
            this.currentImageTranslateTargetLang = 'zh';

            // 绑定语言交换按钮
            const swapBtn = document.getElementById('image-swap-languages-btn');
            if (swapBtn) {
                swapBtn.addEventListener('click', () => {
                    this.swapImageTranslateLanguages();
                });
            }
        }).catch(error => {
            console.error('加载图片翻译语言配置失败:', error);
        });
    }

    // 初始化图片翻译贴合模式选择器
    initImageTranslatePasteModeSelector() {
        const button = document.getElementById('image-translate-paste-mode');
        const menu = document.getElementById('image-translate-paste-menu');

        if (!button || !menu) return;

        // 初始化默认结果模式
        this.currentImageTranslatePasteMode = 1; // 默认图片结果

        // 设置菜单中的默认选中状态
        const defaultOption = menu.querySelector('[data-paste="1"]'); // 默认选中图片结果
        if (defaultOption) {
            menu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
            defaultOption.classList.add('selected');
        }

        // 绑定按钮点击事件
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });

        // 绑定选项点击事件
        menu.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                const pasteMode = parseInt(option.dataset.paste);
                const emojiElement = option.querySelector('.language-emoji');
                const label = option.querySelector('.language-label').textContent;

                // 更新菜单中的选中状态
                menu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');

                // 更新按钮显示
                const textSpan = button.querySelector('.language-text');
                if (textSpan && emojiElement) {
                    // 克隆emoji元素以保持SVG结构完整
                    const clonedEmoji = emojiElement.cloneNode(true);
                    const labelSpan = document.createElement('span');
                    labelSpan.className = 'language-label';
                    labelSpan.textContent = label;

                    // 清空并重新构建按钮内容
                    textSpan.innerHTML = '';
                    textSpan.appendChild(clonedEmoji);
                    textSpan.appendChild(labelSpan);
                }

                // 保存当前选择
                this.currentImageTranslatePasteMode = pasteMode;

                // 如果已有翻译结果，检查是否需要重新翻译
                if (this.currentImageTranslateResult) {
                    this.handleModeSwitch(pasteMode);
                }



                menu.style.display = 'none';
            }
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            menu.style.display = 'none';
        });
    }

    // 初始化单个图片翻译语言选择器
    initImageTranslateLanguageSelector(buttonId, menuId, options, defaultLangcode, onLanguageChange) {
        const button = document.getElementById(buttonId);
        const menu = document.getElementById(menuId);

        if (!button || !menu) return;

        // 生成语言选项
        menu.innerHTML = options.map(lang => {
            const isSelected = lang.langcode === defaultLangcode;
            return `
                <div class="language-option ${isSelected ? 'selected' : ''}" data-langcode="${lang.langcode}">
                    <span class="language-emoji">${lang.emoji}</span>
                    <span class="language-label">${lang.label}</span>
                </div>
            `;
        }).join('');

        // 设置默认语言
        const defaultLang = options.find(lang => lang.langcode === defaultLangcode) || options[0];
        this.updateImageTranslateLanguageButton(button, defaultLang);

        // 绑定按钮点击事件
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = menu.style.display === 'block';

            // 关闭所有其他语言菜单
            document.querySelectorAll('.language-select-menu').forEach(m => {
                if (m !== menu) m.style.display = 'none';
            });

            menu.style.display = isOpen ? 'none' : 'block';
        });

        // 绑定选项点击事件
        menu.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                const langcode = option.dataset.langcode;
                const lang = options.find(l => l.langcode === langcode);

                if (lang) {
                    // 更新菜单中的选中状态
                    menu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');

                    this.updateImageTranslateLanguageButton(button, lang);
                    menu.style.display = 'none';
                    onLanguageChange(langcode);
                }
            }
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            menu.style.display = 'none';
        });
    }

    // 更新图片翻译语言按钮显示
    updateImageTranslateLanguageButton(button, lang) {
        const textSpan = button.querySelector('.language-text');
        if (textSpan) {
            textSpan.innerHTML = `<span class="language-emoji">${lang.emoji}</span><span class="language-label">${lang.label}</span>`;
        }
    }

    // 交换图片翻译语言
    swapImageTranslateLanguages() {
        // 检查当前源语言是否为自动检测
        if (this.currentImageTranslateSourceLang === 'auto') {
            this.showNotification('源语言为自动检测时无法交换', 'warning');
            return;
        }

        // 导入图片翻译专用语言配置
        import('./language-config.js').then(({ imageTranslateLanguageOptions, ImageTranslateLanguages }) => {
            // 获取当前语言
            const currentSource = this.currentImageTranslateSourceLang;
            const currentTarget = this.currentImageTranslateTargetLang;

            // 交换语言
            const newSource = currentTarget;
            const newTarget = currentSource;

            // 更新源语言选择器
            const sourceLang = imageTranslateLanguageOptions.find(lang => lang.langcode === newSource);
            if (sourceLang) {
                const sourceButton = document.getElementById('image-translate-source-language');
                this.updateImageTranslateLanguageButton(sourceButton, sourceLang);
                this.currentImageTranslateSourceLang = newSource;
            }

            // 更新目标语言选择器
            const targetLang = imageTranslateLanguageOptions.find(lang => lang.langcode === newTarget);
            if (targetLang) {
                const targetButton = document.getElementById('image-translate-target-language');
                this.updateImageTranslateLanguageButton(targetButton, targetLang);
                this.currentImageTranslateTargetLang = newTarget;
            }


        }).catch(error => {
            console.error('语言交换失败:', error);
            this.showNotification('语言交换失败', 'error');
        });
    }

    // 显示图片翻译页面的操作按钮
    showImageTranslateButtons() {
        const translateResultArea = document.querySelector('#image-translate-view .translate-result-area');
        if (translateResultArea) {
            translateResultArea.classList.add('show-buttons');
        }
        this.clearHideImageTranslateButtonsTimer();
    }

    // 隐藏图片翻译页面的操作按钮
    hideImageTranslateButtons() {
        const translateResultArea = document.querySelector('#image-translate-view .translate-result-area');
        if (translateResultArea) {
            translateResultArea.classList.remove('show-buttons');
        }
        this.clearHideImageTranslateButtonsTimer();
    }

    // 清除隐藏图片翻译按钮的定时器
    clearHideImageTranslateButtonsTimer() {
        if (this.hideImageTranslateButtonsTimer) {
            clearTimeout(this.hideImageTranslateButtonsTimer);
            this.hideImageTranslateButtonsTimer = null;
        }
    }

    // 延迟隐藏图片翻译按钮
    scheduleHideImageTranslateButtons() {
        this.clearHideImageTranslateButtonsTimer();
        this.hideImageTranslateButtonsTimer = setTimeout(() => {
            this.hideImageTranslateButtons();
        }, 3000); // 3秒后隐藏
    }

    // 复制翻译结果
    async copyTranslateResult() {
        const resultTextarea = document.getElementById('translate-result-text');
        if (!resultTextarea) {
            return;
        }

        const text = resultTextarea.value.trim();
        if (!text) {
            this.showNotification('没有翻译结果可复制', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('翻译结果已复制到剪贴板', 'success');

            // 检查是否启用翻译页面复制后自动关闭插件
            const config = window.ocrPlugin?.configManager?.getConfig();
            // 优先使用新配置，如果没有则使用旧配置（向后兼容）
            const shouldAutoClose = config?.ui?.autoCloseTranslate !== undefined ?
                config.ui.autoCloseTranslate === true :
                config?.ui?.autoClose === true;
            if (shouldAutoClose) {
                setTimeout(() => {
                    if (window.utools) {
                        window.utools.hideMainWindow();
                    }
                }, 300);
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败', 'error');
        }
    }

    // 清空翻译内容
    clearTranslateContent(showNotification = true, resetSmartDetection = true) {
        const sourceTextarea = document.getElementById('translate-source-text');
        const resultTextarea = document.getElementById('translate-result-text');

        // 检查是否有内容需要清空
        const hasSourceContent = sourceTextarea && sourceTextarea.value.trim();
        const hasResultContent = resultTextarea && resultTextarea.value.trim();
        const hasTranslateResults = Object.keys(this.translateResults).length > 0;

        if (!hasSourceContent && !hasResultContent && !hasTranslateResults) {
            if (showNotification) {
                this.showNotification('没有内容需要清空', 'info');
            }
            return;
        }

        if (sourceTextarea) {
            sourceTextarea.value = '';
        }

        if (resultTextarea) {
            resultTextarea.value = '';
            // 恢复默认占位符
            this.restoreTranslateDefaultPlaceholder();
        }

        // 清空多模型翻译结果数据
        this.translateResults = {};
        this.currentDisplayModel = null;

        // 重新渲染模型标签以清除状态指示器
        this.renderModelTabs();

        // 根据参数决定是否重置智能检测状态
        if (resetSmartDetection) {
            this.resetSmartDetection();
        }

        if (showNotification) {
            this.showNotification('翻译内容已清空', 'success');
        }
    }

    // 静默清空翻译内容（用于页面切换和插件退出时）
    clearTranslateContentSilently() {
        const sourceTextarea = document.getElementById('translate-source-text');
        const resultTextarea = document.getElementById('translate-result-text');

        // 检查是否有内容需要清空
        const hasSourceContent = sourceTextarea && sourceTextarea.value.trim();
        const hasResultContent = resultTextarea && resultTextarea.value.trim();

        // 如果没有内容，直接返回
        if (!hasSourceContent && !hasResultContent) {
            return;
        }

        // 清空输入框内容
        if (sourceTextarea) {
            sourceTextarea.value = '';
        }

        // 清空结果区域内容
        if (resultTextarea) {
            resultTextarea.value = '';
            // 恢复默认占位符
            this.restoreTranslateDefaultPlaceholder();
        }

        // 重置智能检测状态
        this.resetSmartDetection();

        // 清除任何翻译相关的临时数据
        this.clearTranslateTemporaryData();
    }

    // 静默清空图片翻译内容（用于页面切换和插件退出时）
    clearImageTranslateContentSilently() {
        // 检查是否有内容需要清空
        const hasImageContent = this.currentImageTranslateBase64;
        const hasResultContent = this.currentImageTranslateResult;

        // 如果没有内容，直接返回
        if (!hasImageContent && !hasResultContent) {
            return;
        }

        // 清空当前图片数据
        this.currentImageTranslateBase64 = null;

        // 隐藏图片预览，显示占位符
        const preview = document.getElementById('image-translate-preview');
        const placeholder = document.getElementById('image-translate-placeholder');

        if (preview && placeholder) {
            preview.style.display = 'none';
            placeholder.style.display = 'flex';
        }

        // 销毁输入图片的缩放管理器
        if (this.imageTranslateInputZoomManager) {
            this.imageTranslateInputZoomManager.destroy();
            this.imageTranslateInputZoomManager = null;
        }

        // 清空翻译结果（调用现有方法保持一致性）
        this.clearImageTranslateResult();

        // 隐藏操作按钮
        this.hideImageTranslateButtons();

        // 清空保存的图片
        const ocrPlugin = this.getOcrPlugin();
        if (ocrPlugin) {
            ocrPlugin.lastImageBase64 = null;
        }


    }

    // 静默清空OCR内容（用于页面切换和插件退出时）
    clearOCRContentSilently() {
        // 清空原始识别结果文本
        this.originalResultText = '';

        // 清空DOM中的识别结果
        const resultText = document.getElementById('result-text');
        const rawResultText = document.getElementById('raw-result-text');
        const renderedResult = document.getElementById('rendered-result');

        if (resultText) {
            resultText.value = '';
        }

        if (rawResultText) {
            rawResultText.value = '';
        }

        if (renderedResult) {
            renderedResult.innerHTML = '';
        }

        // 清空图片预览
        this.clearImagePreview();

        // 清空保存的图片
        if (window.ocrPlugin) {
            window.ocrPlugin.lastImageBase64 = null;
        }

        // 清除任何OCR相关的临时数据
        this.clearOCRTemporaryData();
    }

    // 清除OCR相关的临时数据
    clearOCRTemporaryData() {
        // 清除跳转来源标识
        if (window.shouldFillFromOCR) {
            delete window.shouldFillFromOCR;
        }
    }

    // 清除翻译相关的临时数据
    clearTranslateTemporaryData() {
        // 清除预设的翻译文本
        if (window.pendingTranslateText) {
            delete window.pendingTranslateText;
        }

        // 清除跳转来源标识
        if (window.shouldFillFromOCR) {
            delete window.shouldFillFromOCR;
        }

        // 清除翻译输入框聚焦标志
        if (this._translateInputFocused) {
            this._translateInputFocused = false;
        }

        // 清除任何正在进行的翻译请求（如果有的话）
        if (this.currentTranslateRequest) {
            // 这里可以添加取消翻译请求的逻辑
            this.currentTranslateRequest = null;
        }
    }

    // 交换源语言和目标语言
    swapLanguages() {
        const sourceMenu = document.getElementById('translate-source-menu');
        const targetMenu = document.getElementById('translate-target-menu');
        const sourceBtn = document.getElementById('translate-source-language');
        const targetBtn = document.getElementById('translate-target-language');
        const sourceTextarea = document.getElementById('translate-source-text');
        const resultTextarea = document.getElementById('translate-result-text');

        if (!sourceMenu || !targetMenu || !sourceBtn || !targetBtn || !sourceTextarea || !resultTextarea) {
            return;
        }

        // 获取当前选中的语言
        const sourceSelected = sourceMenu.querySelector('.language-option.selected');
        const targetSelected = targetMenu.querySelector('.language-option.selected');

        if (!sourceSelected || !targetSelected) {
            return;
        }

        // 移除自动检测限制，现在所有语言都可以交换

        // 交换选中状态
        const sourceValue = sourceSelected.getAttribute('data-value');
        const targetValue = targetSelected.getAttribute('data-value');

        // 更新源语言
        sourceMenu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
        const newSourceOption = sourceMenu.querySelector(`[data-value="${targetValue}"]`);
        if (newSourceOption) {
            newSourceOption.classList.add('selected');
            const sourceBtnText = sourceBtn.querySelector('.language-text');
            if (sourceBtnText) {
                const emojiSpan = newSourceOption.querySelector('span:first-child');
                const labelSpan = newSourceOption.querySelector('span:last-child');
                if (emojiSpan && labelSpan) {
                    sourceBtnText.innerHTML = `<span class="language-emoji">${emojiSpan.textContent}</span><span class="language-label">${labelSpan.textContent}</span>`;
                } else {
                    sourceBtnText.textContent = newSourceOption.textContent;
                }
            }
        }

        // 更新目标语言
        targetMenu.querySelectorAll('.language-option').forEach(opt => opt.classList.remove('selected'));
        const newTargetOption = targetMenu.querySelector(`[data-value="${sourceValue}"]`);
        if (newTargetOption) {
            newTargetOption.classList.add('selected');
            const targetBtnText = targetBtn.querySelector('.language-text');
            if (targetBtnText) {
                const emojiSpan = newTargetOption.querySelector('span:first-child');
                const labelSpan = newTargetOption.querySelector('span:last-child');
                if (emojiSpan && labelSpan) {
                    targetBtnText.innerHTML = `<span class="language-emoji">${emojiSpan.textContent}</span><span class="language-label">${labelSpan.textContent}</span>`;
                } else {
                    targetBtnText.textContent = newTargetOption.textContent;
                }
            }
        }

        // 交换文本内容
        const tempText = sourceTextarea.value;
        sourceTextarea.value = resultTextarea.value;
        resultTextarea.value = tempText;

        // 如果结果区域为空，恢复默认占位符
        if (!resultTextarea.value.trim()) {
            this.restoreTranslateDefaultPlaceholder();
        }

        // 保存语言设置
        this.saveLanguageSettings();

        // 标记用户手动操作，暂停智能检测
        this.userManuallyChanged = true;

        this.showNotification('已交换源语言和目标语言', 'success');
    }

    // 保存翻译历史记录
    saveTranslateHistory(sourceText, targetText, sourceLanguage, targetLanguage) {
        try {
            const history = {
                id: Date.now().toString(),
                sourceText: sourceText,
                targetText: targetText,
                sourceLanguage: sourceLanguage ? sourceLanguage.langCode : 'zh-cn',
                targetLanguage: targetLanguage.langCode,
                timestamp: new Date().toISOString(),
                createdAt: Date.now()
            };

            // 获取现有历史记录
            const stored = this.getStorageItem('translateHistory');
            let existingHistory = [];
            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                existingHistory = typeof stored === 'string' ? JSON.parse(stored) : (Array.isArray(stored) ? stored : []);
            }

            // 添加新记录到开头
            existingHistory.unshift(history);

            // 限制历史记录数量，使用与OCR历史记录相同的配置管理方式
            let maxHistory = 100; // 默认值
            if (window.ocrPlugin && window.ocrPlugin.historyManager) {
                // 使用historyManager的配置，确保与OCR记录保持一致
                window.ocrPlugin.historyManager.updateMaxHistoryCount();
                maxHistory = window.ocrPlugin.historyManager.maxHistoryCount;
            } else {
                // 降级方案：直接读取配置
                maxHistory = this.config?.ui?.historyMaxCount || 100;
            }

            if (existingHistory.length > maxHistory) {
                existingHistory.splice(maxHistory);
            }

            // 保存到存储
            const isUToolsStorage = typeof utools !== 'undefined' && utools.dbStorage && utools.dbStorage.setItem;
            const dataToStore = isUToolsStorage ? existingHistory : JSON.stringify(existingHistory);
            this.setStorageItem('translateHistory', dataToStore);
        } catch (error) {
            console.error('保存翻译历史记录失败:', error);
        }
    }

    // 获取翻译历史记录
    getTranslateHistory() {
        try {
            const stored = this.getStorageItem('translateHistory');
            if (stored) {
                // uTools的dbStorage可能直接返回对象，localStorage返回字符串
                return typeof stored === 'string' ? JSON.parse(stored) : (Array.isArray(stored) ? stored : []);
            }
            return [];
        } catch (error) {
            console.error('获取翻译历史记录失败:', error);
            return [];
        }
    }

    // 清空翻译历史记录
    clearTranslateHistory() {
        try {
            this.removeStorageItem('translateHistory');
            this.showNotification('翻译历史记录已清空', 'success');
        } catch (error) {
            console.error('清空翻译历史记录失败:', error);
            this.showNotification('清空历史记录失败', 'error');
        }
    }

    // 设置翻译按钮控制
    setupTranslateButtonsControl() {
        // 先清理之前的事件监听器
        this.cleanupTranslateButtonsControl();

        // 防止重复绑定事件
        if (this.translateButtonsControlInitialized) {
            return;
        }

        const translateResultArea = document.querySelector('.translate-result-area');
        const bottomHoverArea = document.querySelector('.translate-bottom-hover-area');

        if (!translateResultArea || !bottomHoverArea) {
            return;
        }

        // 创建事件处理函数（保存引用以便后续清理）
        this.translateButtonsEventHandlers = {
            bottomHoverEnter: () => {
                this.showTranslateButtons();
            },
            bottomHoverLeave: (e) => {
                // 检查鼠标是否移动到了按钮上，如果是则不隐藏
                if (!this.isMouseOverTranslateButtons(e.relatedTarget)) {
                    this.startHideTranslateButtonsTimer();
                }
            },
            resultAreaEnter: () => {
                this.clearHideTranslateButtonsTimer();
            },
            resultAreaLeave: (e) => {
                // 检查鼠标是否移动到了按钮上，如果是则不隐藏
                if (!this.isMouseOverTranslateButtons(e.relatedTarget)) {
                    this.startHideTranslateButtonsTimer();
                }
            }
        };

        // 绑定事件
        bottomHoverArea.addEventListener('mouseenter', this.translateButtonsEventHandlers.bottomHoverEnter);
        bottomHoverArea.addEventListener('mouseleave', this.translateButtonsEventHandlers.bottomHoverLeave);
        translateResultArea.addEventListener('mouseenter', this.translateButtonsEventHandlers.resultAreaEnter);
        translateResultArea.addEventListener('mouseleave', this.translateButtonsEventHandlers.resultAreaLeave);

        // 为每个按钮添加鼠标事件处理
        this.setupTranslateButtonHoverEvents();

        // 标记已初始化
        this.translateButtonsControlInitialized = true;
    }

    // 显示翻译按钮
    showTranslateButtons() {
        const translateResultArea = document.querySelector('.translate-result-area');
        if (translateResultArea) {
            translateResultArea.classList.add('show-buttons');
        }
        this.clearHideTranslateButtonsTimer();
    }

    // 开始隐藏按钮的计时器
    startHideTranslateButtonsTimer() {
        this.clearHideTranslateButtonsTimer();
        this.translateButtonsTimer = setTimeout(() => {
            this.hideTranslateButtons();
        }, 600); // 0.6秒延迟
    }

    // 清除隐藏按钮的计时器
    clearHideTranslateButtonsTimer() {
        if (this.translateButtonsTimer) {
            clearTimeout(this.translateButtonsTimer);
            this.translateButtonsTimer = null;
        }
    }

    // 隐藏翻译按钮
    hideTranslateButtons() {
        const translateResultArea = document.querySelector('.translate-result-area');
        if (translateResultArea) {
            translateResultArea.classList.remove('show-buttons');
        }
        this.clearHideTranslateButtonsTimer();
    }

    // 检查鼠标是否在翻译按钮上
    isMouseOverTranslateButtons(element) {
        if (!element) return false;

        // 检查元素本身或其父元素是否是翻译按钮或模型导航按钮
        let current = element;
        while (current && current !== document.body) {
            if (current.classList && (
                current.classList.contains('translate-action-btn') ||
                current.classList.contains('translate-model-tab') ||
                current.classList.contains('translate-model-tabs')
            )) {
                return true;
            }
            current = current.parentElement;
        }
        return false;
    }

    // 设置翻译按钮悬停事件
    setupTranslateButtonHoverEvents() {
        const translateButtons = document.querySelectorAll('.translate-action-btn');

        // 清理之前的按钮事件处理器
        if (this.translateButtonEventHandlers) {
            this.translateButtonEventHandlers.forEach((handlers, button) => {
                button.removeEventListener('mouseenter', handlers.enter);
                button.removeEventListener('mouseleave', handlers.leave);
            });
        }

        // 初始化事件处理器映射
        this.translateButtonEventHandlers = new Map();

        translateButtons.forEach(button => {
            // 创建事件处理函数
            const handlers = {
                enter: () => {
                    this.clearHideTranslateButtonsTimer();
                    this.showTranslateButtons(); // 确保按钮保持显示
                },
                leave: (e) => {
                    // 检查鼠标是否移动到了其他按钮或检测区域
                    if (!this.isMouseOverTranslateButtons(e.relatedTarget) &&
                        !this.isMouseOverBottomHoverArea(e.relatedTarget)) {
                        this.startHideTranslateButtonsTimer();
                    }
                }
            };

            // 绑定事件
            button.addEventListener('mouseenter', handlers.enter);
            button.addEventListener('mouseleave', handlers.leave);

            // 保存处理器引用
            this.translateButtonEventHandlers.set(button, handlers);
        });

        // 为模型导航容器添加鼠标事件处理
        this.setupModelTabsHoverEvents();
    }

    // 设置模型导航悬停事件
    setupModelTabsHoverEvents() {
        const modelTabsContainer = document.getElementById('translate-model-tabs');
        if (!modelTabsContainer) return;

        // 清理之前的事件处理器
        if (this.modelTabsEventHandlers) {
            modelTabsContainer.removeEventListener('mouseenter', this.modelTabsEventHandlers.enter);
            modelTabsContainer.removeEventListener('mouseleave', this.modelTabsEventHandlers.leave);
        }

        // 创建事件处理函数
        this.modelTabsEventHandlers = {
            enter: () => {
                this.clearHideTranslateButtonsTimer();
                this.showTranslateButtons(); // 确保按钮保持显示
            },
            leave: (e) => {
                // 检查鼠标是否移动到了其他按钮或检测区域
                if (!this.isMouseOverTranslateButtons(e.relatedTarget) &&
                    !this.isMouseOverBottomHoverArea(e.relatedTarget)) {
                    this.startHideTranslateButtonsTimer();
                }
            }
        };

        // 绑定事件
        modelTabsContainer.addEventListener('mouseenter', this.modelTabsEventHandlers.enter);
        modelTabsContainer.addEventListener('mouseleave', this.modelTabsEventHandlers.leave);
    }

    // 检查鼠标是否在底部检测区域
    isMouseOverBottomHoverArea(element) {
        if (!element) return false;

        let current = element;
        while (current && current !== document.body) {
            if (current.classList && current.classList.contains('translate-bottom-hover-area')) {
                return true;
            }
            current = current.parentElement;
        }
        return false;
    }

    // 清理翻译按钮控制事件
    cleanupTranslateButtonsControl() {
        // 清理主要区域的事件监听器
        if (this.translateButtonsEventHandlers) {
            const translateResultArea = document.querySelector('.translate-result-area');
            const bottomHoverArea = document.querySelector('.translate-bottom-hover-area');

            if (translateResultArea && bottomHoverArea) {
                bottomHoverArea.removeEventListener('mouseenter', this.translateButtonsEventHandlers.bottomHoverEnter);
                bottomHoverArea.removeEventListener('mouseleave', this.translateButtonsEventHandlers.bottomHoverLeave);
                translateResultArea.removeEventListener('mouseenter', this.translateButtonsEventHandlers.resultAreaEnter);
                translateResultArea.removeEventListener('mouseleave', this.translateButtonsEventHandlers.resultAreaLeave);
            }

            this.translateButtonsEventHandlers = null;
        }

        // 清理按钮事件处理器
        if (this.translateButtonEventHandlers) {
            this.translateButtonEventHandlers.forEach((handlers, button) => {
                button.removeEventListener('mouseenter', handlers.enter);
                button.removeEventListener('mouseleave', handlers.leave);
            });
            this.translateButtonEventHandlers = null;
        }

        // 清理模型导航事件处理器
        if (this.modelTabsEventHandlers) {
            const modelTabsContainer = document.getElementById('translate-model-tabs');
            if (modelTabsContainer) {
                modelTabsContainer.removeEventListener('mouseenter', this.modelTabsEventHandlers.enter);
                modelTabsContainer.removeEventListener('mouseleave', this.modelTabsEventHandlers.leave);
            }
            this.modelTabsEventHandlers = null;
        }

        // 清理计时器
        this.clearHideTranslateButtonsTimer();

        // 重置初始化状态
        this.translateButtonsControlInitialized = false;
    }

    // 转换语言代码为传统翻译API支持的格式
    convertLanguageCodeForTraditional(langCode, service) {
        // 定义各个传统翻译服务支持的语言映射
        const languageMaps = {
            baidu: {
                'auto': 'auto',
                'zh-cn': 'zh',
                'zh-tw': 'cht',
                'en-us': 'en',
                'en-gb': 'en',
                'ja-jp': 'jp',
                'ko-kr': 'kor',
                'fr-fr': 'fra',
                'de-de': 'de',
                'es-es': 'spa',
                'it-it': 'it',
                'ru-ru': 'ru',
                'pt-pt': 'pt',
                'ar-sa': 'ara',
                'th-th': 'th',
                'vi-vn': 'vie'
            },
            tencent: {
                'auto': 'auto',
                'zh-cn': 'zh',
                'zh-tw': 'zh-TW',
                'en-us': 'en',
                'en-gb': 'en',
                'ja-jp': 'ja',
                'ko-kr': 'ko',
                'fr-fr': 'fr',
                'de-de': 'de',
                'es-es': 'es',
                'it-it': 'it',
                'ru-ru': 'ru',
                'pt-pt': 'pt',
                'ar-sa': 'ar',
                'th-th': 'th',
                'vi-vn': 'vi',
                'ms-my': 'ms',
                'tr-tr': 'tr'
            },
            aliyun: {
                'auto': 'auto',
                'zh-cn': 'zh',
                'zh-tw': 'zh-tw',
                'en-us': 'en',
                'en-gb': 'en',
                'ja-jp': 'ja',
                'ko-kr': 'ko',
                'fr-fr': 'fr',
                'de-de': 'de',
                'es-es': 'es',
                'it-it': 'it',
                'ru-ru': 'ru',
                'pt-pt': 'pt',
                'ar-sa': 'ar',
                'th-th': 'th',
                'vi-vn': 'vi'
            }
        };

        const serviceMap = languageMaps[service];
        if (!serviceMap) {
            throw new Error(`不支持的翻译服务: ${service}`);
        }

        const convertedCode = serviceMap[langCode];
        if (!convertedCode) {
            // 获取服务显示名称
            const serviceNames = {
                baidu: '百度智能云翻译',
                tencent: '腾讯云翻译',
                aliyun: '阿里云翻译'
            };
            const serviceName = serviceNames[service] || service;
            
            // 获取语言显示名称
            const languageNames = {
                'zh-cn': '简体中文',
                'zh-tw': '繁体中文', 
                'en-us': '英语',
                'ja-jp': '日语',
                'ko-kr': '韩语',
                'fr-fr': '法语',
                'de-de': '德语',
                'es-es': '西班牙语',
                'it-it': '意大利语',
                'ru-ru': '俄语',
                'pt-pt': '葡萄牙语',
                'ar-sa': '阿拉伯语',
                'th-th': '泰语',
                'vi-vn': '越南语',
                'ms-my': '马来语',
                'tr-tr': '土耳其语',
                'nl-nl': '荷兰语',
                'sv-se': '瑞典语',
                'da-dk': '丹麦语',
                'no-no': '挪威语',
                'fi-fi': '芬兰语',
                'pl-pl': '波兰语',
                'cs-cz': '捷克语',
                'hu-hu': '匈牙利语',
                'ro-ro': '罗马尼亚语'
            };
            const languageName = languageNames[langCode] || langCode;
            
            throw new Error(`${serviceName}不支持${languageName}，请选择其他语言或切换翻译服务`);
        }

        return convertedCode;
    }

    // 初始化自定义工具提示
    initCustomTooltips() {
        let currentTooltip = null;
        let showTimeout = null;
        let hideTimeout = null;

        // 兼容性函数：查找最近的匹配选择器的祖先元素
        const findClosest = (element, selector) => {
            if (!element) return null;

            // 如果支持 closest 方法，直接使用
            if (element.closest) {
                return element.closest(selector);
            }

            // 手动实现 closest 功能
            let current = element;
            while (current && current !== document) {
                // 检查当前元素是否匹配选择器
                if (this.matchesSelector(current, selector)) {
                    return current;
                }
                current = current.parentElement;
            }
            return null;
        };

        // 兼容性函数：检查元素是否匹配选择器
        const matchesSelector = (element, selector) => {
            if (!element) return false;

            // 标准方法
            if (element.matches) {
                return element.matches(selector);
            }
            // Webkit 前缀
            if (element.webkitMatchesSelector) {
                return element.webkitMatchesSelector(selector);
            }
            // Mozilla 前缀
            if (element.mozMatchesSelector) {
                return element.mozMatchesSelector(selector);
            }
            // IE 前缀
            if (element.msMatchesSelector) {
                return element.msMatchesSelector(selector);
            }

            // 最后的兼容方案：简单的属性检查
            if (selector === '[data-tooltip]') {
                return element.hasAttribute && element.hasAttribute('data-tooltip');
            }

            return false;
        };

        // 将 matchesSelector 绑定到 this 上下文
        this.matchesSelector = matchesSelector;

        // 为所有带有 data-tooltip 属性的元素添加事件监听
        document.addEventListener('mouseenter', (e) => {
            const target = findClosest(e.target, '[data-tooltip]');
            if (!target) return;

            const tooltipText = target.getAttribute('data-tooltip');
            if (!tooltipText) return;

            // 清除之前的定时器
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }

            // 延迟显示工具提示
            showTimeout = setTimeout(() => {
                this.showCustomTooltip(target, tooltipText);
                currentTooltip = target;
            }, 250); // 250ms 延迟显示
        }, true);

        document.addEventListener('mouseleave', (e) => {
            const target = findClosest(e.target, '[data-tooltip]');
            if (!target) return;

            // 清除显示定时器
            if (showTimeout) {
                clearTimeout(showTimeout);
                showTimeout = null;
            }

            // 延迟隐藏工具提示
            hideTimeout = setTimeout(() => {
                this.hideCustomTooltip();
                currentTooltip = null;
            }, 100); // 100ms 延迟隐藏
        }, true);

        // 点击时立即隐藏工具提示
        document.addEventListener('click', () => {
            if (showTimeout) {
                clearTimeout(showTimeout);
                showTimeout = null;
            }
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }
            this.hideCustomTooltip();
            currentTooltip = null;
        });
    }

    // 显示自定义工具提示
    showCustomTooltip(target, text) {
        // 移除现有的工具提示
        this.hideCustomTooltip();

        // 创建工具提示元素
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = text;
        tooltip.id = 'custom-tooltip';

        // 添加到页面
        document.body.appendChild(tooltip);

        // 计算位置
        const targetRect = target.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        // 默认显示在目标元素上方居中
        let left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
        let top = targetRect.top - tooltipRect.height - 10;

        // 边界检查和调整
        const padding = 10;
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // 水平边界检查
        if (left < padding) {
            left = padding;
        } else if (left + tooltipRect.width > windowWidth - padding) {
            left = windowWidth - tooltipRect.width - padding;
        }

        // 垂直边界检查 - 如果上方空间不够，显示在下方
        if (top < padding) {
            top = targetRect.bottom + 10;
            // 调整箭头方向（通过CSS类）- 显示在下方时箭头指向上方
            tooltip.classList.add('tooltip-bottom');
        }

        // 设置位置
        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';

        // 显示工具提示
        requestAnimationFrame(() => {
            tooltip.classList.add('show');
        });
    }

    // 隐藏自定义工具提示
    hideCustomTooltip() {
        const tooltip = document.getElementById('custom-tooltip');
        if (tooltip) {
            tooltip.classList.remove('show');
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200); // 等待动画完成
        }
    }

    // 初始化个人中心配置
    initPersonalCenterConfig() {
        // 防止重复初始化，避免重复绑定事件监听器
        if (this.personalCenterConfigInitialized) {
            // 已初始化，只重新加载数据
            this.loadPersonalCenterData();
            return;
        }

        // 绑定头像点击事件（更换头像）
        const userAvatar = document.getElementById('user-avatar');
        if (userAvatar) {
            userAvatar.addEventListener('click', () => {
                this.handleChangeAvatar();
            });
        }

        // 绑定用户名编辑功能
        this.initUserNameEdit();

        // 初始化隐藏的重置功能
        this.initHiddenResetFeature();

        // 初始化确认对话框
        this.initResetConfirmDialog();

        // 初始化隐藏彩蛋功能
        this.initHiddenQuotaEasterEgg();

        // 标记已初始化
        this.personalCenterConfigInitialized = true;

        // 加载个人中心数据
        this.loadPersonalCenterData();
    }

    // 初始化用户名编辑功能
    initUserNameEdit() {
        // 防止重复绑定事件监听器
        if (this.userNameEditInitialized) {
            return;
        }

        const userNameInput = document.getElementById('user-name');

        if (userNameInput) {
            // 双击用户名进入编辑模式
            userNameInput.addEventListener('dblclick', () => {
                this.enableUserNameEdit();
            });

            // 失去焦点时保存
            userNameInput.addEventListener('blur', () => {
                this.disableUserNameEdit();
            });

            // 回车键保存
            userNameInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    userNameInput.blur();
                }
                if (e.key === 'Escape') {
                    this.cancelUserNameEdit();
                }
            });

            // 标记已初始化
            this.userNameEditInitialized = true;
        }
    }

    // 启用用户名编辑
    enableUserNameEdit() {
        const userNameInput = document.getElementById('user-name');

        if (userNameInput) {
            this.originalUserName = userNameInput.value;
            userNameInput.removeAttribute('readonly');
            userNameInput.focus();
            userNameInput.select();
        }
    }

    // 禁用用户名编辑并保存
    disableUserNameEdit() {
        const userNameInput = document.getElementById('user-name');

        if (userNameInput && !userNameInput.hasAttribute('readonly')) {
            userNameInput.setAttribute('readonly', 'readonly');
            this.saveUserInfo();
        }
    }

    // 取消用户名编辑
    cancelUserNameEdit() {
        const userNameInput = document.getElementById('user-name');

        if (userNameInput && this.originalUserName !== undefined) {
            userNameInput.value = this.originalUserName;
            userNameInput.setAttribute('readonly', 'readonly');
            userNameInput.blur();
        }
    }

    // 初始化隐藏的重置功能（连续点击5次触发）
    initHiddenResetFeature() {
        const titleElement = document.getElementById('user-info-title');
        if (!titleElement) return;

        // 防止重复绑定事件监听器
        if (this.hiddenResetFeatureInitialized) {
            return;
        }

        // 隐藏重置功能的状态管理
        this.hiddenResetState = {
            clickCount: 0,
            firstClickTime: 0,  // 第一次点击的时间
            timeWindow: 2500,   // 2.5秒固定时间窗口
            requiredClicks: 5,  // 需要连续点击5次
            timeoutId: null     // 计时器ID
        };

        // 创建事件处理函数的引用，便于后续移除（如果需要）
        this.hiddenResetClickHandler = () => {
            this.handleHiddenResetClick();
        };

        // 绑定点击事件 - 使用passive监听器提高性能
        titleElement.addEventListener('click', this.hiddenResetClickHandler, { passive: true });

        // 防止文本选择和右键菜单，保持隐蔽性
        titleElement.addEventListener('selectstart', (e) => e.preventDefault());
        titleElement.addEventListener('contextmenu', (e) => e.preventDefault());

        // 标记已初始化
        this.hiddenResetFeatureInitialized = true;
    }

    // 处理隐藏重置功能的点击事件
    handleHiddenResetClick() {
        // 安全检查：确保状态对象存在
        if (!this.hiddenResetState) return;

        const currentTime = Date.now();
        const state = this.hiddenResetState;

        // 如果是第一次点击或者之前的时间窗口已过期，开始新的时间窗口
        if (state.clickCount === 0 || (state.timeoutId === null)) {
            // 开始新的时间窗口
            state.clickCount = 1;
            state.firstClickTime = currentTime;

            // 设置2.5秒后的重置计时器
            state.timeoutId = setTimeout(() => {
                // 时间窗口结束，重置计数器
                this.resetHiddenResetState();
            }, state.timeWindow);
        } else {
            // 在时间窗口内，增加计数
            state.clickCount++;
        }

        // 检查是否达到触发条件
        if (state.clickCount >= state.requiredClicks) {
            // 清除计时器
            if (state.timeoutId) {
                clearTimeout(state.timeoutId);
                state.timeoutId = null;
            }

            // 重置计数器
            this.resetHiddenResetState();

            // 显示确认对话框
            this.showResetConfirmDialog();
        }
    }

    // 重置隐藏重置功能的状态
    resetHiddenResetState() {
        if (this.hiddenResetState) {
            // 清除计时器
            if (this.hiddenResetState.timeoutId) {
                clearTimeout(this.hiddenResetState.timeoutId);
            }

            // 重置状态
            this.hiddenResetState.clickCount = 0;
            this.hiddenResetState.firstClickTime = 0;
            this.hiddenResetState.timeoutId = null;
        }
    }



    // 初始化确认对话框
    initResetConfirmDialog() {
        // 防止重复绑定事件监听器
        if (this.resetConfirmDialogInitialized) {
            return;
        }

        const modal = document.getElementById('reset-confirm-modal');
        const closeBtn = document.getElementById('reset-modal-close');
        const cancelBtn = document.getElementById('reset-cancel-btn');
        const confirmBtn = document.getElementById('reset-confirm-btn');

        if (modal && closeBtn && cancelBtn && confirmBtn) {
            // 关闭对话框
            const closeModal = () => {
                modal.style.display = 'none';
            };

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // 确认重置
            confirmBtn.addEventListener('click', () => {
                closeModal();
                this.handleRefreshUserInfo();
            });

            // 标记已初始化
            this.resetConfirmDialogInitialized = true;
        }
    }

    // 显示重置确认对话框
    showResetConfirmDialog() {
        const modal = document.getElementById('reset-confirm-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 初始化隐藏彩蛋功能（额度奖励）
    initHiddenQuotaEasterEgg() {
        // 防止重复绑定事件监听器
        if (this.hiddenQuotaEasterEggInitialized) {
            return;
        }

        // 获取OCR和翻译额度标题元素
        const ocrQuotaTitle = document.getElementById('ocr-quota-title');
        const translateQuotaTitle = document.getElementById('translate-quota-title');

        if (!ocrQuotaTitle || !translateQuotaTitle) {
            console.warn('[个人中心] 未找到额度标题元素，跳过彩蛋功能初始化');
            return;
        }

        // 初始化彩蛋状态管理（每个标题独立计数，使用新的计时逻辑）
        this.quotaEasterEggStates = {
            ocr: {
                clickCount: 0,
                firstClickTime: 0,  // 第一次点击的时间
                timeWindow: 2500,   // 2.5秒固定时间窗口
                requiredClicks: 5,  // 需要连续点击5次
                timeoutId: null,    // 计时器ID
                type: 'OCR'
            },
            translate: {
                clickCount: 0,
                firstClickTime: 0,  // 第一次点击的时间
                timeWindow: 2500,   // 2.5秒固定时间窗口
                requiredClicks: 5,  // 需要连续点击5次
                timeoutId: null,    // 计时器ID
                type: '翻译'
            }
        };

        // 创建事件处理函数
        this.ocrQuotaClickHandler = () => {
            this.handleQuotaEasterEggClick('ocr');
        };

        this.translateQuotaClickHandler = () => {
            this.handleQuotaEasterEggClick('translate');
        };

        // 绑定点击事件
        ocrQuotaTitle.addEventListener('click', this.ocrQuotaClickHandler, { passive: true });
        translateQuotaTitle.addEventListener('click', this.translateQuotaClickHandler, { passive: true });

        // 防止文本选择，保持隐蔽性
        ocrQuotaTitle.addEventListener('selectstart', (e) => e.preventDefault());
        ocrQuotaTitle.addEventListener('contextmenu', (e) => e.preventDefault());
        translateQuotaTitle.addEventListener('selectstart', (e) => e.preventDefault());
        translateQuotaTitle.addEventListener('contextmenu', (e) => e.preventDefault());

        // 标记已初始化
        this.hiddenQuotaEasterEggInitialized = true;
    }

    // 处理彩蛋点击事件
    handleQuotaEasterEggClick(quotaType) {
        // 安全检查：确保状态对象存在
        if (!this.quotaEasterEggStates || !this.quotaEasterEggStates[quotaType]) return;

        const currentTime = Date.now();
        const state = this.quotaEasterEggStates[quotaType];

        // 如果是第一次点击或者之前的时间窗口已过期，开始新的时间窗口
        if (state.clickCount === 0 || (state.timeoutId === null)) {
            // 开始新的时间窗口
            state.clickCount = 1;
            state.firstClickTime = currentTime;

            // 设置2.5秒后的重置计时器
            state.timeoutId = setTimeout(() => {
                // 时间窗口结束，重置计数器
                this.resetQuotaEasterEggState(quotaType);
            }, state.timeWindow);
        } else {
            // 在时间窗口内，增加计数
            state.clickCount++;
        }

        // 检查是否达到触发条件
        if (state.clickCount >= state.requiredClicks) {
            // 清除计时器
            if (state.timeoutId) {
                clearTimeout(state.timeoutId);
                state.timeoutId = null;
            }

            // 重置计数器
            this.resetQuotaEasterEggState(quotaType);

            // 尝试触发彩蛋奖励
            this.triggerQuotaEasterEgg(quotaType);
        }
    }

    // 重置彩蛋功能的状态
    resetQuotaEasterEggState(quotaType) {
        if (this.quotaEasterEggStates && this.quotaEasterEggStates[quotaType]) {
            const state = this.quotaEasterEggStates[quotaType];

            // 清除计时器
            if (state.timeoutId) {
                clearTimeout(state.timeoutId);
            }

            // 重置状态
            state.clickCount = 0;
            state.firstClickTime = 0;
            state.timeoutId = null;
        }
    }



    // 触发彩蛋奖励
    triggerQuotaEasterEgg(quotaType) {
        // 检查今天是否已经触发过彩蛋
        const today = new Date().toDateString();
        const easterEggKey = `quotaEasterEgg_${today}`;
        const todayEasterEggs = JSON.parse(this.getStorageItem(easterEggKey) || '{}');

        if (todayEasterEggs[quotaType]) {
            // 今天已经触发过这个类型的彩蛋
            const typeName = quotaType === 'ocr' ? 'OCR' : '翻译';
            this.showNotification(`今天已经获得过${typeName}额度奖励了，明天再来试试吧！`, 'info');
            return;
        }

        // 执行彩蛋奖励
        this.executeQuotaEasterEggReward(quotaType, today, easterEggKey, todayEasterEggs);
    }

    // 执行彩蛋奖励
    executeQuotaEasterEggReward(quotaType, today, easterEggKey, todayEasterEggs) {
        const typeName = quotaType === 'ocr' ? 'OCR' : '翻译';
        const rewardAmount = 20;

        try {
            // 如果有OCR Pro额度管理器，使用其方法
            if (window.ocrProQuotaManager) {
                const currentStatus = window.ocrProQuotaManager.getQuotaStatus();
                const currentRemaining = quotaType === 'ocr' ? currentStatus.ocrRemaining : currentStatus.translateRemaining;
                const newRemaining = currentRemaining + rewardAmount;

                // 临时启用调试模式以允许超出默认限制
                const originalDebugMode = window.ocrProQuotaManager.debugMode;
                window.ocrProQuotaManager.debugMode = true;

                // 更新额度
                if (quotaType === 'ocr') {
                    window.ocrProQuotaManager.setOCRQuota(newRemaining);
                } else {
                    window.ocrProQuotaManager.setTranslateQuota(newRemaining);
                }

                // 恢复原始调试模式状态
                window.ocrProQuotaManager.debugMode = originalDebugMode;

                // 记录今天已触发的彩蛋
                todayEasterEggs[quotaType] = {
                    triggered: true,
                    timestamp: Date.now(),
                    rewardAmount: rewardAmount,
                    originalAmount: currentRemaining,
                    newAmount: newRemaining
                };
                this.setStorageItem(easterEggKey, JSON.stringify(todayEasterEggs));

                // 显示奖励消息
                this.showNotification(`🎉 恭喜你触发彩蛋，奖励${rewardAmount}次${typeName}免费额度！`, 'success');

                // 彩蛋额度奖励已发放
            } else {
                // 兜底逻辑：使用原有的统计数据系统
                this.executeQuotaEasterEggRewardFallback(quotaType, typeName, rewardAmount, today, easterEggKey, todayEasterEggs);
            }
        } catch (error) {
            console.error(`[隐藏彩蛋] 发放${typeName}额度奖励失败:`, error);
            this.showNotification(`发放${typeName}额度奖励时出现错误`, 'error');
        }
    }

    // 兜底逻辑：使用原有统计数据系统发放彩蛋奖励
    executeQuotaEasterEggRewardFallback(quotaType, typeName, rewardAmount, today, easterEggKey, todayEasterEggs) {
        // 这里可以实现兜底逻辑，暂时只显示消息
        this.showNotification(`🎉 恭喜你触发彩蛋，奖励${rewardAmount}次${typeName}免费额度！（需要OCR Pro额度管理器支持）`, 'info');

        // 记录今天已触发的彩蛋
        todayEasterEggs[quotaType] = {
            triggered: true,
            timestamp: Date.now(),
            rewardAmount: rewardAmount
        };
        this.setStorageItem(easterEggKey, JSON.stringify(todayEasterEggs));

        // 彩蛋已触发（兜底模式）
    }

    // 处理重置用户信息（隐藏功能）
    handleRefreshUserInfo() {
        // 重置首次使用标志，重新获取uTools信息
        this.resetFirstTimeUserFlag().then(() => {
            return this.checkFirstTimeUser();
        }).then(() => {
            this.showNotification('用户信息已重置', 'success');
        }).catch((error) => {
            console.error('[个人中心] 重置用户信息失败:', error);
            this.showNotification('重置失败: ' + error.message, 'error');
        });
    }

    // 重置首次使用标志
    async resetFirstTimeUserFlag() {
        try {
            const userIdentifier = await this.getUserIdentifier();
            const firstTimeKey = `isFirstTimeUser_${userIdentifier}`;

            // 删除首次使用标志
            this.removeStorageItem(firstTimeKey);

            // 清除本地用户信息，强制重新获取
            this.removeStorageItem('userInfo');

            // 已重置首次使用标志
        } catch (error) {
            console.error('[个人中心] 重置首次使用标志失败:', error);
            throw error;
        }
    }

    // 处理更换头像
    handleChangeAvatar() {
        // 创建文件输入元素
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const avatarImg = e.target.result;
                    this.updateUserAvatar(avatarImg);
                    this.saveUserInfo();
                };
                reader.readAsDataURL(file);
            }
        });

        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }

    // 更新用户头像
    updateUserAvatar(avatarData) {
        const userAvatar = document.getElementById('user-avatar');
        if (userAvatar && avatarData) {
            userAvatar.innerHTML = `<img src="${avatarData}" alt="用户头像">`;
        }
    }

    // 保存用户信息
    saveUserInfo() {
        const userNameInput = document.getElementById('user-name');

        // 保存用户编辑的信息
        const userInfo = {};

        // 始终保存用户名（允许用户自定义）
        if (userNameInput) {
            userInfo.name = userNameInput.value || '';
        }

        // 保存当前头像（包括从uTools获取的头像）
        const currentAvatarSrc = document.querySelector('#user-avatar img')?.src || '';
        if (currentAvatarSrc) {
            userInfo.avatar = currentAvatarSrc;
        }

        // 保存到本地存储
        this.setStorageItem('userInfo', JSON.stringify(userInfo));
    }



    // 处理个人设置变化
    handlePersonalSettingChange(settingKey, value) {
        const personalSettings = JSON.parse(this.getStorageItem('personalSettings') || '{}');
        personalSettings[settingKey] = value;
        this.setStorageItem('personalSettings', JSON.stringify(personalSettings));

        // 设置已更新
    }

    // 切换使用统计区域显示
    toggleUsageStatsSection(show) {
        const statsSection = document.getElementById('usage-stats-section');
        if (statsSection) {
            statsSection.style.display = show ? 'block' : 'none';
        }
    }



    // 加载个人中心数据
    loadPersonalCenterData() {
        // 检查是否为首次使用
        this.checkFirstTimeUser().then(() => {
            // 更新使用统计显示
            this.updateUsageStatsDisplay();
        });
    }

    // 检查是否为首次使用
    async checkFirstTimeUser() {
        try {
            // 获取用户唯一标识
            const userIdentifier = await this.getUserIdentifier();
            const firstTimeKey = `isFirstTimeUser_${userIdentifier}`;

            // 检查是否已经标记为非首次使用
            const isFirstTime = !this.getStorageItem(firstTimeKey);

            // 用户标识和首次使用状态已确定

            if (isFirstTime) {
                // 首次使用，获取uTools用户信息作为默认信息
                await this.loadUToolsUserInfoForFirstTime();

                // 标记为已使用
                this.setStorageItem(firstTimeKey, 'true');
            } else {
                // 非首次使用，加载本地存储的用户信息
                this.loadLocalUserInfo();
            }
        } catch (error) {
            console.error('[个人中心] 检查首次使用状态失败:', error);
            // 出错时加载本地用户信息
            this.loadLocalUserInfo();
        }
    }

    // 获取用户唯一标识
    async getUserIdentifier() {
        try {
            // 优先使用uTools用户信息作为标识
            if (typeof utools !== 'undefined') {
                const utoolsUser = utools.getUser();
                if (utoolsUser && utoolsUser.nickname) {
                    // 使用用户昵称的hash作为标识（简化处理）
                    return this.simpleHash(utoolsUser.nickname);
                }

                // 如果没有用户信息，使用设备ID
                const nativeId = utools.getNativeId();
                if (nativeId) {
                    return this.simpleHash(nativeId);
                }
            }

            // 如果都获取不到，使用浏览器指纹作为备选方案
            return this.getBrowserFingerprint();
        } catch (error) {
            console.error('[个人中心] 获取用户标识失败:', error);
            // 最后的备选方案
            return this.getBrowserFingerprint();
        }
    }

    // 简单的字符串hash函数
    simpleHash(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString();
    }

    // 获取浏览器指纹作为备选标识
    getBrowserFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Browser fingerprint', 2, 2);

        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');

        return this.simpleHash(fingerprint);
    }

    // 首次使用时从uTools获取用户信息
    async loadUToolsUserInfoForFirstTime() {
        try {
            // 检查是否在uTools环境中
            if (typeof utools === 'undefined') {
                this.setDefaultUserInfo();
                return;
            }

            // 获取uTools用户信息
            const utoolsUser = utools.getUser();
            if (!utoolsUser) {
                this.setDefaultUserInfo();
                return;
            }

            // 更新用户名
            const userNameInput = document.getElementById('user-name');
            if (userNameInput && utoolsUser.nickname) {
                userNameInput.value = utoolsUser.nickname;
                userNameInput.readOnly = false;
                userNameInput.title = '双击编辑用户名';
            }

            // 更新头像
            if (utoolsUser.avatar) {
                this.updateUserAvatar(utoolsUser.avatar);
            }

            // 保存到本地存储作为用户的初始信息
            this.saveUserInfo();
        } catch (error) {
            console.error('[个人中心] 首次获取uTools用户信息失败:', error);
            this.setDefaultUserInfo();
        }
    }

    // 设置默认用户信息
    setDefaultUserInfo() {
        const userNameInput = document.getElementById('user-name');
        if (userNameInput) {
            userNameInput.value = 'OCR用户';
            userNameInput.readOnly = false;
            userNameInput.title = '双击编辑用户名';
        }

        // 保存默认信息
        this.saveUserInfo();
    }

    // 从uTools获取用户信息（用于刷新功能）
    async loadUToolsUserInfo() {
        try {
            // 检查是否在uTools环境中
            if (typeof utools === 'undefined') {
                throw new Error('不在uTools环境中');
            }

            // 获取uTools用户信息
            const utoolsUser = utools.getUser();
            if (!utoolsUser) {
                throw new Error('用户未登录uTools');
            }

            // 获取到uTools用户信息

            // 更新用户名
            const userNameInput = document.getElementById('user-name');
            if (userNameInput && utoolsUser.nickname) {
                userNameInput.value = utoolsUser.nickname;
                // 允许用户编辑用户名
                userNameInput.readOnly = false;
                userNameInput.title = '双击编辑用户名';
            }

            // 更新头像
            if (utoolsUser.avatar) {
                this.updateUserAvatar(utoolsUser.avatar);
            }



            // 保存uTools用户信息到本地存储作为缓存
            const utoolsUserInfo = {
                name: utoolsUser.nickname,
                avatar: utoolsUser.avatar,
                type: utoolsUser.type,
                source: 'utools',
                lastUpdate: new Date().toISOString()
            };
            this.setStorageItem('utoolsUserInfo', JSON.stringify(utoolsUserInfo));

            return utoolsUser;
        } catch (error) {
            console.error('[个人中心] 获取uTools用户信息失败:', error);
            throw error;
        }
    }

    // 加载缓存的用户信息
    loadCachedUserInfo() {
        // 优先加载缓存的uTools用户信息
        const utoolsUserInfo = JSON.parse(this.getStorageItem('utoolsUserInfo') || '{}');
        const userNameInput = document.getElementById('user-name');

        if (utoolsUserInfo.name && userNameInput) {
            userNameInput.value = utoolsUserInfo.name;
            userNameInput.readOnly = false;
            userNameInput.title = '双击编辑用户名';

            // 更新头像
            if (utoolsUserInfo.avatar) {
                this.updateUserAvatar(utoolsUserInfo.avatar);
            }

            // 已加载缓存的uTools用户信息
        } else {
            // 如果没有缓存的uTools信息，加载本地存储的信息
            this.loadLocalUserInfo();
        }

        // 加载个人设置
        this.loadPersonalSettings();

        // 加载使用统计
        this.updateUsageStatsDisplay();
    }

    // 加载本地存储的用户信息
    loadLocalUserInfo() {
        const userInfo = JSON.parse(this.getStorageItem('userInfo') || '{}');
        const userNameInput = document.getElementById('user-name');

        // 设置用户名
        if (userNameInput) {
            if (userInfo.name) {
                userNameInput.value = userInfo.name;
            } else {
                // 如果没有保存的用户名，使用默认值
                userNameInput.value = 'OCR用户';
            }
            userNameInput.readOnly = false;
            userNameInput.title = '双击编辑用户名';
        }

        // 设置头像
        if (userInfo.avatar) {
            this.updateUserAvatar(userInfo.avatar);
        }
    }

    // 加载个人设置
    loadPersonalSettings() {
        // 个人设置现在主要通过可选配置页面管理
        // 这里保留方法以维持兼容性，但不再处理具体的开关
    }

    // 更新使用统计显示
    updateUsageStatsDisplay() {
        // 检查并重置OCR Pro额度（如果需要）
        if (window.ocrProQuotaManager) {
            window.ocrProQuotaManager.checkAndResetIfNeeded();
        }

        const stats = JSON.parse(this.getStorageItem('usageStats') || '{}');

        // 检查是否需要重置今日统计
        const today = new Date().toDateString();
        if (stats.lastUpdateDate !== today) {
            stats.todayOcrCount = 0;
            stats.todayTranslateCount = 0;
            stats.lastUpdateDate = today;
            this.setStorageItem('usageStats', JSON.stringify(stats));
        }

        // 更新显示
        const totalOcrElement = document.getElementById('total-ocr-count');
        const totalTranslateElement = document.getElementById('total-translate-count');
        const todayOcrElement = document.getElementById('today-ocr-count');
        const todayTranslateElement = document.getElementById('today-translate-count');

        if (totalOcrElement) totalOcrElement.textContent = stats.totalOcrCount || 0;
        if (totalTranslateElement) totalTranslateElement.textContent = stats.totalTranslateCount || 0;
        if (todayOcrElement) todayOcrElement.textContent = stats.todayOcrCount || 0;
        if (todayTranslateElement) todayTranslateElement.textContent = stats.todayTranslateCount || 0;

        // 更新进度条
        this.updateUsageProgressBars(stats);
    }

    // 更新使用额度进度条
    updateUsageProgressBars(stats) {
        // 如果有OCR Pro额度管理器，使用其数据更新显示
        if (window.ocrProQuotaManager) {
            window.ocrProQuotaManager.updateUIDisplay();
            return;
        }

        // 兜底逻辑：使用原有的统计数据
        // 设置默认额度（可以从配置中读取）
        const ocrLimit = 30;
        const translateLimit = 30;

        // 获取今日使用量
        const todayOcrCount = stats.todayOcrCount || 0;
        const todayTranslateCount = stats.todayTranslateCount || 0;

        // 计算剩余额度
        const ocrRemaining = Math.max(ocrLimit - todayOcrCount, 0);
        const translateRemaining = Math.max(translateLimit - todayTranslateCount, 0);

        // 更新OCR进度条
        const ocrUsageText = document.getElementById('ocr-usage-text');
        const ocrProgressFill = document.getElementById('ocr-progress-fill');

        if (ocrUsageText) {
            ocrUsageText.textContent = `${ocrRemaining}/${ocrLimit}`;
        }

        if (ocrProgressFill) {
            const ocrPercentage = Math.min((ocrRemaining / ocrLimit) * 100, 100);
            ocrProgressFill.style.width = `${ocrPercentage}%`;

            // 根据剩余额度改变颜色
            if (ocrRemaining === 0) {
                ocrProgressFill.style.background = '#ff4757'; // 红色 - 额度用完
            } else if (ocrRemaining <= 5) {
                ocrProgressFill.style.background = '#ff6b7a'; // 浅红色 - 严重警告
            } else if (ocrRemaining <= 15) {
                ocrProgressFill.style.background = '#ffa502'; // 橙色 - 警告
            } else {
                ocrProgressFill.style.background = 'linear-gradient(90deg, #1976d2, #42a5f5)'; // 蓝色渐变
                ocrProgressFill.style.boxShadow = '0 1px 3px rgba(25, 118, 210, 0.3)';
            }
        }

        // 更新翻译进度条
        const translateUsageText = document.getElementById('translate-usage-text');
        const translateProgressFill = document.getElementById('translate-progress-fill');

        if (translateUsageText) {
            translateUsageText.textContent = `${translateRemaining}/${translateLimit}`;
        }

        if (translateProgressFill) {
            const translatePercentage = Math.min((translateRemaining / translateLimit) * 100, 100);
            translateProgressFill.style.width = `${translatePercentage}%`;

            // 根据剩余额度改变颜色
            if (translateRemaining === 0) {
                translateProgressFill.style.background = '#ff4757'; // 红色 - 额度用完
            } else if (translateRemaining <= 5) {
                translateProgressFill.style.background = '#ff6b7a'; // 浅红色 - 严重警告
            } else if (translateRemaining <= 15) {
                translateProgressFill.style.background = '#ffa502'; // 橙色 - 警告
            } else {
                translateProgressFill.style.background = 'linear-gradient(90deg, #1976d2, #42a5f5)'; // 蓝色渐变
                translateProgressFill.style.boxShadow = '0 1px 3px rgba(25, 118, 210, 0.3)';
            }
        }
    }

    // 增加使用统计计数（供其他模块调用）
    incrementUsageStats(type) {
        const stats = JSON.parse(this.getStorageItem('usageStats') || '{}');
        const today = new Date().toDateString();

        // 初始化统计数据
        if (!stats.totalOcrCount) stats.totalOcrCount = 0;
        if (!stats.totalTranslateCount) stats.totalTranslateCount = 0;
        if (!stats.todayOcrCount) stats.todayOcrCount = 0;
        if (!stats.todayTranslateCount) stats.todayTranslateCount = 0;

        // 检查是否需要重置今日统计
        if (stats.lastUpdateDate !== today) {
            stats.todayOcrCount = 0;
            stats.todayTranslateCount = 0;
            stats.lastUpdateDate = today;
        }

        // 增加计数
        if (type === 'ocr') {
            stats.totalOcrCount++;
            stats.todayOcrCount++;
        } else if (type === 'translate') {
            stats.totalTranslateCount++;
            stats.todayTranslateCount++;
        }

        // 保存统计数据
        this.setStorageItem('usageStats', JSON.stringify(stats));

        // 更新显示（如果当前在个人中心页面）
        const personalCenterConfig = document.getElementById('personal-center-config');
        if (personalCenterConfig && personalCenterConfig.style.display !== 'none') {
            this.updateUsageStatsDisplay();
        }
    }

    // 初始化备份恢复配置
    initBackupRestoreConfig() {
        // 防止重复绑定事件监听器
        if (this.backupRestoreEventsInitialized) {
            // 如果事件已经绑定，只刷新数据状态
            this.refreshDataStatus();
            return;
        }

        // 绑定备份按钮事件
        const createBackupBtn = document.getElementById('create-backup-btn');
        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => this.showCreateBackupConfirm());
        }

        // 绑定恢复按钮事件
        const restoreBackupBtn = document.getElementById('restore-backup-btn');
        if (restoreBackupBtn) {
            restoreBackupBtn.addEventListener('click', () => this.handleRestoreBackup());
        }

        // 绑定文件选择事件
        const restoreFileInput = document.getElementById('restore-file-input');
        const restoreDropZone = document.getElementById('restore-drop-zone');

        if (restoreFileInput && restoreDropZone) {
            // 点击选择文件
            restoreDropZone.addEventListener('click', () => {
                this.showUploadBackupConfirm();
            });

            // 文件选择事件
            restoreFileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleFileSelected(e.target.files[0]);
                }
            });

            // 拖拽事件
            restoreDropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                restoreDropZone.classList.add('drag-over');
            });

            restoreDropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                restoreDropZone.classList.remove('drag-over');
            });

            restoreDropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                restoreDropZone.classList.remove('drag-over');

                if (e.dataTransfer.files.length > 0) {
                    // 保存拖拽的文件，等待用户确认
                    this.pendingDropFile = e.dataTransfer.files[0];
                    this.showUploadBackupConfirm();
                }
            });
        }

        // 绑定文件移除按钮事件
        const fileRemoveBtn = document.querySelector('.file-remove-btn');
        if (fileRemoveBtn) {
            fileRemoveBtn.addEventListener('click', () => this.clearSelectedFile());
        }

        // 绑定刷新状态按钮事件
        const refreshStatusBtn = document.getElementById('refresh-data-status-btn');
        if (refreshStatusBtn) {
            refreshStatusBtn.addEventListener('click', () => this.refreshDataStatus(true)); // 传入true表示手动刷新
        }

        // 设置备份恢复管理器的回调
        if (window.backupRestoreManager) {
            window.backupRestoreManager.setCallbacks({
                onProgress: (message, percentage) => this.updateProgress(message, percentage),
                onSuccess: (data) => this.handleBackupRestoreSuccess(data),
                onError: (error) => this.handleBackupRestoreError(error)
            });
        }

        // 初始化备份确认对话框
        this.initBackupConfirmDialogs();

        // 标记事件已初始化
        this.backupRestoreEventsInitialized = true;

        // 初始化时刷新数据状态（自动刷新，不显示提示）
        this.refreshDataStatus();
    }

    // 处理创建备份
    async handleCreateBackup() {
        if (!window.backupRestoreManager) {
            this.showNotification('备份管理器未初始化', 'error');
            return;
        }

        try {
            // 获取四个复选框的状态
            const includeConfig = document.getElementById('backup-include-config').checked;
            const includePersonal = document.getElementById('backup-include-personal').checked;
            const includeHistory = document.getElementById('backup-include-history').checked;
            const includeCache = document.getElementById('backup-include-cache').checked;

            const createBackupBtn = document.getElementById('create-backup-btn');
            const backupProgress = document.getElementById('backup-progress');

            // 显示进度条，隐藏按钮
            createBackupBtn.style.display = 'none';
            backupProgress.style.display = 'flex';

            const backupData = await window.backupRestoreManager.createBackup({
                includeConfig,
                includePersonal,
                includeHistory,
                includeCache
            });

            // 自动下载备份文件
            const filename = window.backupRestoreManager.generateBackupFilename({
                includeHistory,
                includeCache
            });

            await window.backupRestoreManager.exportBackupToFile(backupData, filename);

            this.showNotification('备份创建成功并已下载', 'success');

        } catch (error) {
            console.error('创建备份失败:', error);
            this.showNotification('创建备份失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮显示，隐藏进度条
            const createBackupBtn = document.getElementById('create-backup-btn');
            const backupProgress = document.getElementById('backup-progress');
            createBackupBtn.style.display = 'flex';
            backupProgress.style.display = 'none';
        }
    }

    // 处理文件选择
    async handleFileSelected(file) {
        if (!file.name.endsWith('.json')) {
            this.showNotification('请选择有效的JSON备份文件', 'error');
            return;
        }

        try {
            const backupData = await window.backupRestoreManager.importBackupFromFile(file);
            const summary = window.backupRestoreManager.getBackupSummary(backupData);

            // 显示文件信息
            this.displayFileInfo(file, summary);

            // 显示恢复选项
            document.getElementById('restore-options').style.display = 'block';
            document.getElementById('restore-actions').style.display = 'flex';

            // 根据备份内容设置默认选项
            document.getElementById('restore-config').checked = summary.items.config > 0;
            document.getElementById('restore-history').checked = summary.items.history > 0;
            document.getElementById('restore-cache').checked = summary.items.cache > 0;

            this.selectedBackupData = backupData;

        } catch (error) {
            console.error('读取备份文件失败:', error);
            this.showNotification('备份文件格式无效: ' + error.message, 'error');
        }
    }

    // 显示文件信息
    displayFileInfo(file, summary) {
        const fileInfo = document.getElementById('restore-file-info');
        const fileName = fileInfo.querySelector('.file-name');
        const fileDetails = fileInfo.querySelector('.file-details');

        fileName.textContent = file.name;

        const details = [
            `文件大小: ${this.formatFileSize(file.size)}`,
            `备份时间: ${new Date(summary.timestamp).toLocaleString()}`,
            `配置项: ${summary.items.config}个`,
            `历史记录: ${summary.items.history}个`,
            `缓存项: ${summary.items.cache}个`
        ];

        fileDetails.innerHTML = details.join('<br>');

        // 隐藏拖拽区域，显示文件信息
        document.getElementById('restore-drop-zone').style.display = 'none';
        fileInfo.style.display = 'block';
    }

    // 清除选择的文件
    clearSelectedFile() {
        document.getElementById('restore-drop-zone').style.display = 'block';
        document.getElementById('restore-file-info').style.display = 'none';
        document.getElementById('restore-options').style.display = 'none';
        document.getElementById('restore-actions').style.display = 'none';
        document.getElementById('restore-file-input').value = '';
        this.selectedBackupData = null;
    }

    // 处理恢复备份
    async handleRestoreBackup() {
        if (!this.selectedBackupData) {
            this.showNotification('请先选择备份文件', 'error');
            return;
        }

        if (!window.backupRestoreManager) {
            this.showNotification('备份管理器未初始化', 'error');
            return;
        }

        try {
            const restoreConfig = document.getElementById('restore-config').checked;
            const restorePersonal = document.getElementById('restore-personal').checked;
            const restoreHistory = document.getElementById('restore-history').checked;
            const restoreCache = document.getElementById('restore-cache').checked;
            const clearExisting = document.getElementById('clear-existing').checked;

            if (!restoreConfig && !restorePersonal && !restoreHistory && !restoreCache) {
                this.showNotification('请至少选择一项要恢复的内容', 'error');
                return;
            }

            // 确认对话框
            const confirmMessage = clearExisting
                ? '确定要清除所有现有数据并恢复备份吗？此操作不可撤销！'
                : '确定要恢复选中的备份数据吗？';

            if (!confirm(confirmMessage)) {
                return;
            }

            const restoreBackupBtn = document.getElementById('restore-backup-btn');
            const restoreProgress = document.getElementById('restore-progress');

            // 显示进度条，隐藏按钮
            restoreBackupBtn.style.display = 'none';
            restoreProgress.style.display = 'flex';

            await window.backupRestoreManager.restoreBackup(this.selectedBackupData, {
                restoreConfig,
                restorePersonal,
                restoreHistory,
                restoreCache,
                clearExisting
            });

            this.showNotification('备份恢复成功！建议重启插件以确保所有功能正常', 'success');

            // 清除文件选择
            this.clearSelectedFile();

            // 刷新数据状态（自动刷新，不显示提示）
            setTimeout(() => this.refreshDataStatus(), 1000);

        } catch (error) {
            console.error('恢复备份失败:', error);
            this.showNotification('恢复备份失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮显示，隐藏进度条
            const restoreBackupBtn = document.getElementById('restore-backup-btn');
            const restoreProgress = document.getElementById('restore-progress');
            restoreBackupBtn.style.display = 'flex';
            restoreProgress.style.display = 'none';
        }
    }

    // 刷新数据状态
    async refreshDataStatus(isManualRefresh = false) {
        if (!window.backupRestoreManager) {
            return;
        }

        const summaryContainer = document.getElementById('current-data-summary');
        const refreshBtn = document.getElementById('refresh-data-status-btn');

        // 设置加载状态
        summaryContainer.innerHTML = '<div class="summary-loading">正在加载数据状态...</div>';

        // 禁用刷新按钮并显示加载状态
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.style.opacity = '0.6';
            refreshBtn.style.cursor = 'not-allowed';
        }

        try {
            const summary = await window.backupRestoreManager.getCurrentDataSummary();
            this.displayDataSummary(summary);

            // 只在手动刷新时显示成功提示
            if (isManualRefresh) {
                this.showNotification('数据状态已刷新', 'success');
            }
        } catch (error) {
            console.error('获取数据状态失败:', error);
            summaryContainer.innerHTML = '<div class="summary-loading">获取数据状态失败</div>';
            // 错误提示始终显示，无论是否手动刷新
            this.showNotification('刷新数据状态失败: ' + error.message, 'error');
        } finally {
            // 恢复刷新按钮状态
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.style.opacity = '1';
                refreshBtn.style.cursor = 'pointer';
            }
        }
    }

    // 显示数据状态摘要
    displayDataSummary(summary) {
        const summaryContainer = document.getElementById('current-data-summary');

        // 重新分类数据
        const categories = this.categorizeDataSummary(summary);

        let html = '';
        categories.forEach(category => {
            const totalSize = category.items.reduce((sum, item) => sum + (item.size || 0), 0);
            const existsCount = category.items.filter(item => item.exists).length;
            const totalCount = category.items.length;

            html += `
                <div class="data-category-item" onclick="showDataDetailModal('${category.key}', '${category.title}')">
                    <div class="data-category-header">
                        <h5 class="data-category-title">${category.title}</h5>
                        <div class="data-category-status">
                            <span class="data-category-count">${existsCount}/${totalCount}</span>
                            <span class="data-category-size">${this.formatFileSize(totalSize)}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        summaryContainer.innerHTML = html;

        // 存储分类数据供弹窗使用
        window.dataSummaryCategories = categories;
    }

    // 数据分类方法
    categorizeDataSummary(summary) {
        const categories = [
            {
                key: 'config',
                title: '配置数据',
                items: []
            },
            {
                key: 'personal',
                title: '个人使用信息',
                items: []
            },
            {
                key: 'history',
                title: '历史记录',
                items: []
            },
            {
                key: 'cache',
                title: '缓存数据',
                items: []
            }
        ];

        // 分类配置数据
        Object.entries(summary.config || {}).forEach(([key, info]) => {
            categories[0].items.push({
                key,
                displayName: key,
                ...info
            });
        });

        // 分类历史记录和个人数据
        Object.entries(summary.history || {}).forEach(([key, info]) => {
            const personalDataKeys = ['userInfo', 'usageStats', 'personalSettings', 'utoolsUserInfo', 'utoolsUserInfoInitialized', 'translateLanguageSettings'];
            const historyDataKeys = ['ocr_histories', 'translateHistory'];

            let displayName = key;
            let categoryIndex = 2; // 默认为历史记录

            // 个人数据分类
            if (personalDataKeys.includes(key) || key.startsWith('isFirstTimeUser_')) {
                categoryIndex = 1; // 个人使用信息
                if (key === 'userInfo') displayName = '用户信息';
                else if (key === 'usageStats') displayName = '使用统计';
                else if (key === 'personalSettings') displayName = '个人设置';
                else if (key === 'utoolsUserInfo') displayName = 'uTools用户信息';
                else if (key === 'utoolsUserInfoInitialized') displayName = 'uTools初始化标志';
                else if (key === 'translateLanguageSettings') displayName = '翻译语言设置';
                else if (key.startsWith('isFirstTimeUser_')) displayName = '首次使用标志';
            }
            // 历史记录分类
            else if (historyDataKeys.includes(key)) {
                if (key === 'ocr_histories') displayName = 'OCR历史记录';
                else if (key === 'translateHistory') displayName = '翻译历史记录';
            }
            // 其他归为缓存数据
            else {
                categoryIndex = 3;
            }

            categories[categoryIndex].items.push({
                key,
                displayName,
                ...info
            });
        });

        // 分类缓存数据
        Object.entries(summary.cache || {}).forEach(([key, info]) => {
            let displayName = key;
            if (key.endsWith('_models_fetch_time')) {
                const platform = key.replace('_models_fetch_time', '');
                displayName = `${platform}模型获取时间`;
            } else if (key.endsWith('_fetched_models')) {
                const platform = key.replace('_fetched_models', '');
                displayName = `${platform}模型列表`;
            } else if (key.includes('status_cache')) {
                displayName = key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            }

            categories[3].items.push({
                key,
                displayName,
                ...info
            });
        });

        return categories;
    }

    // 更新进度显示
    updateProgress(message, percentage) {
        const progressElements = document.querySelectorAll('.backup-progress');
        progressElements.forEach(progressEl => {
            if (progressEl.style.display !== 'none') {
                const progressFill = progressEl.querySelector('.progress-fill');
                const progressText = progressEl.querySelector('.progress-text');

                if (progressFill) {
                    progressFill.style.width = `${percentage}%`;
                }
                if (progressText) {
                    progressText.textContent = message;
                }
            }
        });
    }

    // 处理备份恢复成功
    handleBackupRestoreSuccess(data) {
        // 成功处理已在具体方法中实现
    }

    // 处理备份恢复错误
    handleBackupRestoreError(error) {
        this.showNotification('操作失败: ' + error.message, 'error');
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 初始化备份确认对话框
    initBackupConfirmDialogs() {
        // 防止重复初始化
        if (this.backupConfirmDialogsInitialized) {
            return;
        }

        // 初始化创建备份确认对话框
        this.initCreateBackupConfirmDialog();

        // 初始化上传备份确认对话框
        this.initUploadBackupConfirmDialog();

        // 标记已初始化
        this.backupConfirmDialogsInitialized = true;
    }

    // 初始化创建备份确认对话框
    initCreateBackupConfirmDialog() {
        const modal = document.getElementById('backup-create-confirm-modal');
        const closeBtn = document.getElementById('backup-create-modal-close');
        const cancelBtn = document.getElementById('backup-create-cancel-btn');
        const confirmBtn = document.getElementById('backup-create-confirm-btn');

        if (modal && closeBtn && cancelBtn && confirmBtn) {
            // 关闭对话框
            const closeModal = () => {
                modal.style.display = 'none';
            };

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // 确认创建备份
            confirmBtn.addEventListener('click', () => {
                closeModal();
                this.handleCreateBackup();
            });
        }
    }

    // 初始化上传备份确认对话框
    initUploadBackupConfirmDialog() {
        const modal = document.getElementById('backup-upload-confirm-modal');
        const closeBtn = document.getElementById('backup-upload-modal-close');
        const cancelBtn = document.getElementById('backup-upload-cancel-btn');
        const confirmBtn = document.getElementById('backup-upload-confirm-btn');

        if (modal && closeBtn && cancelBtn && confirmBtn) {
            // 关闭对话框
            const closeModal = () => {
                modal.style.display = 'none';
                // 清除临时文件引用
                this.pendingDropFile = null;
            };

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // 确认上传备份
            confirmBtn.addEventListener('click', () => {
                closeModal();
                // 如果有拖拽的文件，直接处理；否则打开文件选择器
                if (this.pendingDropFile) {
                    this.handleFileSelected(this.pendingDropFile);
                    this.pendingDropFile = null; // 清除临时文件引用
                } else {
                    this.triggerFileUpload();
                }
            });
        }
    }

    // 显示创建备份确认弹窗
    showCreateBackupConfirm() {
        const modal = document.getElementById('backup-create-confirm-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 显示上传备份确认弹窗
    showUploadBackupConfirm() {
        const modal = document.getElementById('backup-upload-confirm-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 触发文件上传
    triggerFileUpload() {
        const restoreFileInput = document.getElementById('restore-file-input');
        if (restoreFileInput) {
            restoreFileInput.click();
        }
    }

    // 设置图片翻译按钮控制
    setupImageTranslateButtonsControl() {
        // 先清理之前的事件监听器
        this.cleanupImageTranslateButtonsControl();

        // 防止重复绑定事件
        if (this.imageTranslateButtonsControlInitialized) {
            return;
        }

        // 查找图片翻译页面的结果区域和底部检测区域
        const imageTranslateResultArea = document.querySelector('#image-translate-view .translate-result-area');
        const imageTranslateBottomHoverArea = document.querySelector('#image-translate-view .translate-bottom-hover-area');

        if (!imageTranslateResultArea || !imageTranslateBottomHoverArea) {
            return;
        }

        // 创建事件处理函数（保存引用以便后续清理）
        this.imageTranslateButtonsEventHandlers = {
            bottomHoverEnter: () => {
                this.showImageTranslateButtons();
            },
            bottomHoverLeave: (e) => {
                // 检查鼠标是否移动到了按钮上，如果是则不隐藏
                if (!this.isMouseOverImageTranslateButtons(e.relatedTarget)) {
                    this.startHideImageTranslateButtonsTimer();
                }
            },
            resultAreaEnter: () => {
                this.clearHideImageTranslateButtonsTimer();
            },
            resultAreaLeave: (e) => {
                // 检查鼠标是否移动到了按钮上，如果是则不隐藏
                if (!this.isMouseOverImageTranslateButtons(e.relatedTarget)) {
                    this.startHideImageTranslateButtonsTimer();
                }
            }
        };

        // 绑定事件
        imageTranslateBottomHoverArea.addEventListener('mouseenter', this.imageTranslateButtonsEventHandlers.bottomHoverEnter);
        imageTranslateBottomHoverArea.addEventListener('mouseleave', this.imageTranslateButtonsEventHandlers.bottomHoverLeave);
        imageTranslateResultArea.addEventListener('mouseenter', this.imageTranslateButtonsEventHandlers.resultAreaEnter);
        imageTranslateResultArea.addEventListener('mouseleave', this.imageTranslateButtonsEventHandlers.resultAreaLeave);

        // 为每个按钮添加鼠标事件处理
        this.setupImageTranslateButtonHoverEvents();

        // 标记已初始化
        this.imageTranslateButtonsControlInitialized = true;
    }

    // 显示图片翻译按钮
    showImageTranslateButtons() {
        const imageTranslateResultArea = document.querySelector('#image-translate-view .translate-result-area');
        if (imageTranslateResultArea) {
            imageTranslateResultArea.classList.add('show-buttons');
        }
        this.clearHideImageTranslateButtonsTimer();
    }

    // 开始隐藏图片翻译按钮的计时器
    startHideImageTranslateButtonsTimer() {
        this.clearHideImageTranslateButtonsTimer();
        this.imageTranslateButtonsTimer = setTimeout(() => {
            this.hideImageTranslateButtons();
        }, 600); // 0.6秒延迟
    }

    // 清除隐藏图片翻译按钮的计时器
    clearHideImageTranslateButtonsTimer() {
        if (this.imageTranslateButtonsTimer) {
            clearTimeout(this.imageTranslateButtonsTimer);
            this.imageTranslateButtonsTimer = null;
        }
    }

    // 隐藏图片翻译按钮
    hideImageTranslateButtons() {
        const imageTranslateResultArea = document.querySelector('#image-translate-view .translate-result-area');
        if (imageTranslateResultArea) {
            imageTranslateResultArea.classList.remove('show-buttons');
        }
        this.clearHideImageTranslateButtonsTimer();
    }

    // 检查鼠标是否在图片翻译按钮上
    isMouseOverImageTranslateButtons(element) {
        if (!element) return false;

        // 检查元素本身或其父元素是否是翻译按钮或模型导航按钮
        let current = element;
        while (current && current !== document.body) {
            if (current.classList && (
                current.classList.contains('translate-action-btn') ||
                current.classList.contains('translate-model-tab') ||
                current.classList.contains('translate-model-tabs')
            )) {
                return true;
            }
            current = current.parentElement;
        }
        return false;
    }

    // 设置图片翻译按钮悬停事件
    setupImageTranslateButtonHoverEvents() {
        // 获取图片翻译页面的所有操作按钮
        const imageTranslateButtons = document.querySelectorAll('#image-translate-view .translate-action-btn');

        // 初始化事件处理器映射
        this.imageTranslateButtonEventHandlers = new Map();

        imageTranslateButtons.forEach(button => {
            // 创建事件处理函数
            const handlers = {
                enter: () => {
                    this.clearHideImageTranslateButtonsTimer();
                    this.showImageTranslateButtons(); // 确保按钮保持显示
                },
                leave: (e) => {
                    // 检查鼠标是否移动到了其他按钮或检测区域
                    if (!this.isMouseOverImageTranslateButtons(e.relatedTarget) &&
                        !this.isMouseOverImageTranslateBottomHoverArea(e.relatedTarget)) {
                        this.startHideImageTranslateButtonsTimer();
                    }
                }
            };

            // 绑定事件
            button.addEventListener('mouseenter', handlers.enter);
            button.addEventListener('mouseleave', handlers.leave);

            // 保存处理器引用
            this.imageTranslateButtonEventHandlers.set(button, handlers);
        });

        // 为模型导航容器添加鼠标事件处理
        this.setupImageTranslateModelTabsHoverEvents();
    }

    // 设置图片翻译模型导航悬停事件
    setupImageTranslateModelTabsHoverEvents() {
        const modelTabsContainer = document.getElementById('image-translate-model-tabs');
        if (!modelTabsContainer) {
            return;
        }

        // 创建事件处理函数
        this.imageTranslateModelTabsEventHandlers = {
            enter: () => {
                this.clearHideImageTranslateButtonsTimer();
                this.showImageTranslateButtons(); // 确保按钮保持显示
            },
            leave: (e) => {
                // 检查鼠标是否移动到了按钮或检测区域
                if (!this.isMouseOverImageTranslateButtons(e.relatedTarget) &&
                    !this.isMouseOverImageTranslateBottomHoverArea(e.relatedTarget)) {
                    this.startHideImageTranslateButtonsTimer();
                }
            }
        };

        // 绑定事件
        modelTabsContainer.addEventListener('mouseenter', this.imageTranslateModelTabsEventHandlers.enter);
        modelTabsContainer.addEventListener('mouseleave', this.imageTranslateModelTabsEventHandlers.leave);
    }

    // 检查鼠标是否在图片翻译底部检测区域
    isMouseOverImageTranslateBottomHoverArea(element) {
        if (!element) return false;

        let current = element;
        while (current && current !== document.body) {
            if (current.classList && current.classList.contains('translate-bottom-hover-area') &&
                current.closest('#image-translate-view')) {
                return true;
            }
            current = current.parentElement;
        }
        return false;
    }

    // 清理图片翻译按钮控制事件
    cleanupImageTranslateButtonsControl() {
        // 清理主要区域的事件监听器
        if (this.imageTranslateButtonsEventHandlers) {
            const imageTranslateResultArea = document.querySelector('#image-translate-view .translate-result-area');
            const imageTranslateBottomHoverArea = document.querySelector('#image-translate-view .translate-bottom-hover-area');

            if (imageTranslateResultArea && imageTranslateBottomHoverArea) {
                imageTranslateBottomHoverArea.removeEventListener('mouseenter', this.imageTranslateButtonsEventHandlers.bottomHoverEnter);
                imageTranslateBottomHoverArea.removeEventListener('mouseleave', this.imageTranslateButtonsEventHandlers.bottomHoverLeave);
                imageTranslateResultArea.removeEventListener('mouseenter', this.imageTranslateButtonsEventHandlers.resultAreaEnter);
                imageTranslateResultArea.removeEventListener('mouseleave', this.imageTranslateButtonsEventHandlers.resultAreaLeave);
            }

            this.imageTranslateButtonsEventHandlers = null;
        }

        // 清理按钮事件处理器
        if (this.imageTranslateButtonEventHandlers) {
            this.imageTranslateButtonEventHandlers.forEach((handlers, button) => {
                button.removeEventListener('mouseenter', handlers.enter);
                button.removeEventListener('mouseleave', handlers.leave);
            });
            this.imageTranslateButtonEventHandlers = null;
        }

        // 清理模型导航事件处理器
        if (this.imageTranslateModelTabsEventHandlers) {
            const modelTabsContainer = document.getElementById('image-translate-model-tabs');
            if (modelTabsContainer) {
                modelTabsContainer.removeEventListener('mouseenter', this.imageTranslateModelTabsEventHandlers.enter);
                modelTabsContainer.removeEventListener('mouseleave', this.imageTranslateModelTabsEventHandlers.leave);
            }
            this.imageTranslateModelTabsEventHandlers = null;
        }

        // 清理计时器
        this.clearHideImageTranslateButtonsTimer();

        // 重置初始化标志
        this.imageTranslateButtonsControlInitialized = false;
    }
}

// 导出UI管理器
window.UIManager = UIManager;

// 全局函数：打开API Key获取链接
function openApiKeyUrl(url) {
    try {
        if (typeof utools !== 'undefined' && utools.shellOpenExternal) {
            utools.shellOpenExternal(url);
        } else {
            // 如果不在uTools环境中，使用普通的window.open作为后备
            window.open(url, '_blank');
        }
    } catch (error) {
        console.error('打开外部链接失败:', error);
        // 显示错误提示
        if (window.uiManager) {
            window.uiManager.showNotification('无法打开链接，请手动访问：' + url, 'error');
        }
    }
}

// 全局函数：显示数据详情弹窗
function showDataDetailModal(categoryKey, categoryTitle) {
    const modal = document.getElementById('data-detail-modal');
    const title = document.getElementById('data-detail-title');
    const content = document.getElementById('data-detail-content');

    title.textContent = categoryTitle;

    // 获取分类数据
    const categories = window.dataSummaryCategories || [];
    const category = categories.find(cat => cat.key === categoryKey);

    if (!category) {
        content.innerHTML = '<div class="data-detail-item">暂无数据</div>';
        modal.style.display = 'flex';
        return;
    }

    let html = '';
    category.items.forEach(item => {
        const statusClass = item.exists ? 'exists' : (item.error ? 'error' : 'missing');
        const statusText = item.exists ? '存在' : (item.error ? '错误' : '缺失');

        // 修复数据大小显示问题
        let sizeText = '';
        if (item.exists && item.size !== undefined && item.size !== null) {
            sizeText = formatFileSize(item.size);
        }

        // 为动态键添加标识
        let displayName = item.displayName;
        if (item.type === 'dynamic') {
            displayName += ' (动态)';
        }

        html += `
            <div class="data-detail-item">
                <span class="data-detail-item-name">${displayName}</span>
                <div class="data-detail-item-status">
                    <span class="data-detail-status-badge ${statusClass}">${statusText}</span>
                    ${sizeText ? `<span class="data-detail-size">${sizeText}</span>` : ''}
                </div>
            </div>
        `;
    });

    content.innerHTML = html;
    modal.style.display = 'flex';
}

// 全局函数：关闭数据详情弹窗
function closeDataDetailModal() {
    const modal = document.getElementById('data-detail-modal');
    modal.style.display = 'none';
}

// 全局函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
